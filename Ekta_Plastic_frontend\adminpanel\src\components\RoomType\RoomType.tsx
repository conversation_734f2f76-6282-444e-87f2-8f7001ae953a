import { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { useAuth } from "../../context/AuthContext";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface RoomType {
  id: number;
  name: string;
  description: string;
  base_rate: number;
  status: boolean;
  max_adults: number;
  max_children: number;
  hotel_id?: number;
  // hotel_name removed as per requirement
  Hotel?: {
    Hotel_name: string;
  };
  createdAt?: string;
  updatedAt?: string;
}

export const RoomTypeComponent = () => {
  const { TokenFROMLSGet } = useAuth();
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedRoomType, setSelectedRoomType] = useState<RoomType | null>(null);
  const [formData, setFormData] = useState<Partial<RoomType>>({
    name: "",
    description: "",
    status: true,
    base_rate: 0,
    max_adults: 2,
    max_children: 1
    // hotel_id removed as it will be extracted from token in backend
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>("id");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Current user information
  const [currentUserRole, setCurrentUserRole] = useState<string>("");
  const [currentUserHotelId, setCurrentUserHotelId] = useState<number | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Hotel name is no longer needed as we're not displaying it

  const fetchRoomTypes = async () => {
    try {
      console.log("Fetching room types...");

      const response = await userApi.getAllRoomTypes();
      console.log("API response:", response.data);

      // Backend now handles role-based filtering, so we just use the data directly
      let roomTypesData = response.data.roomTypes || [];

      // Sort the data by ID in descending order (newest first)
      roomTypesData = roomTypesData.sort((a: RoomType, b: RoomType) => b.id - a.id);

      console.log("Sorted room types:", roomTypesData);

      setRoomTypes(roomTypesData);
      setTotalItems(roomTypesData.length);
    } catch (error) {
      toast.error("Error fetching room types");
      console.error("Error fetching room types:", error);
    }
  };

  // Fetch current user information directly from token
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = TokenFROMLSGet();
        if (token) {
          // Decode the token to get user information
          const decoded: any = jwtDecode(token);

          // Get hotel_id directly from token (still needed for form submission)
          if (decoded.hotel_id) {
            setCurrentUserHotelId(decoded.hotel_id);

            // Update formData with the hotel_id
            setFormData(prevData => ({
              ...prevData,
              hotel_id: decoded.hotel_id
            }));
          }

          // Get role information from API (for UI purposes)
          if (decoded.role_id) {
            const roleResponse = await userApi.getRoleName(decoded.role_id.toString());
            if (roleResponse.data && roleResponse.data.role_name) {
              setCurrentUserRole(roleResponse.data.role_name);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    fetchCurrentUser();
  }, []);

  useEffect(() => {
    fetchRoomTypes();
  }, [currentUserRole, currentUserHotelId]);

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // Filter room types based on search term
  const filteredRoomTypes = roomTypes.filter((roomType) => {
    const searchLower = search.toLowerCase();
    return (
      roomType.name.toLowerCase().includes(searchLower) ||
      (roomType.status ? "active" : "inactive").includes(searchLower)
    );
  });

  // Sort the filtered room types
  const sortedRoomTypes = [...filteredRoomTypes].sort((a, b) => {
    if (!sortField) return 0;

    if (sortField === 'id') {
      // Sort by ID (higher ID = newer)
      return sortDirection === 'desc' ? b.id - a.id : a.id - b.id;
    }

    if (sortField === 'name') {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();
      return sortDirection === 'asc'
        ? nameA.localeCompare(nameB)
        : nameB.localeCompare(nameA);
    }

    if (sortField === 'base_rate') {
      const priceA = a.base_rate || 0;
      const priceB = b.base_rate || 0;
      return sortDirection === 'asc' ? priceA - priceB : priceB - priceA;
    }

    if (sortField === 'max_adults') {
      const adultsA = a.max_adults || 0;
      const adultsB = b.max_adults || 0;
      return sortDirection === 'asc' ? adultsA - adultsB : adultsB - adultsA;
    }

    if (sortField === 'max_children') {
      const childrenA = a.max_children || 0;
      const childrenB = b.max_children || 0;
      return sortDirection === 'asc' ? childrenA - childrenB : childrenB - childrenA;
    }

    // Default to sorting by ID (newest first) if no other sort field matches
    return sortDirection === 'desc' ? b.id - a.id : a.id - b.id;
  });

  // Paginate the sorted room types
  const paginatedRoomTypes = sortedRoomTypes.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

    const handleSort = (field: string) => {
    let newDirection: "asc" | "desc";

    if (sortField === field) {
      newDirection = sortDirection === "asc" ? "desc" : "asc";
      setSortDirection(newDirection);
    } else {
      // For id, default to desc (newest first)
      // For other fields, default to asc
      newDirection = field === "id" ? "desc" : "asc";
      setSortField(field);
      setSortDirection(newDirection);
    }

    console.log(`Sorting by ${field} in ${newDirection} order`);
  };

  // Open modal for add or edit operation
  const openModal = (type: "add" | "edit", roomType: RoomType | null = null) => {
    setModalType(type);
    setSelectedRoomType(roomType);
    setErrors({});

    // No need to log hotel_id anymore as it will be extracted from token in backend

    if (roomType) {
      // For editing an existing room type
      setFormData({
        name: roomType.name || "",
        description: roomType.description || "",
        status: roomType.status,
        base_rate: roomType.base_rate || 0,
        max_adults: roomType.max_adults || 2,
        max_children: roomType.max_children || 1
        // hotel_id removed as it will be extracted from token in backend
      });
      console.log("Edit modal opened for room type:", roomType.name);
    } else {
      // For adding a new room type
      setFormData({
        name: "",
        description: "",
        status: true,
        base_rate: 0,
        max_adults: 2,
        max_children: 1,
        hotel_id: currentUserHotelId || undefined
      });
      console.log("Add modal opened with hotel_id:", currentUserHotelId);
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedRoomType(null);
    setModalType("");
  };

  // Validate form data
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    // Create a copy of formData to modify
    const validatedData = {...formData};

    if (!validatedData.name?.trim()) {
      newErrors.name = "Room type name is required";
    }

    // We don't need to validate hotel_name anymore as we're using hotel_id from token

    // Always ensure hotel_id is set if we have it
    if (!validatedData.hotel_id && currentUserHotelId) {
      // If currentUserHotelId exists, use it
      validatedData.hotel_id = currentUserHotelId;
      // Update the actual formData
      setFormData(prev => ({
        ...prev,
        hotel_id: currentUserHotelId
      }));
      console.log("Set hotel_id in validation:", currentUserHotelId);
    }

    if (validatedData.max_adults !== undefined && validatedData.max_adults < 1) {
      newErrors.max_adults = "Maximum adults must be at least 1";
    }

    if (validatedData.max_children !== undefined && validatedData.max_children < 0) {
      newErrors.max_children = "Maximum children cannot be negative";
    }

    if (validatedData.base_rate !== undefined && validatedData.base_rate <= 0) {
      newErrors.base_rate = "Base price must be greater than 0";
    }

    console.log("Validation result:", newErrors, "formData:", validatedData);
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save handler for add/edit modal
  const handleSave = async (formData: Partial<RoomType>) => {
    console.log("handleSave called with formData:", formData);
    if (!validateForm()) {
      console.log("Form validation failed", errors);
      return;
    }

    try {
      console.log("Attempting API call...");
      if (modalType === "edit" && selectedRoomType?.id) {
        console.log("Updating room type with ID:", selectedRoomType.id);
        const response = await userApi.updateRoomType(selectedRoomType.id.toString(), formData);
        console.log("Update response:", response);
        toast.success("Room type updated successfully!");
      } else if (modalType === "add") {
        console.log("Creating new room type");
        const response = await userApi.createRoomType(formData);
        console.log("Create response:", response);
        toast.success("Room type added successfully!");

        // Set to first page to show newly added data at the top
        setCurrentPage(1);

        // Ensure sorting is by ID in descending order (newest first)
        setSortField("id");
        setSortDirection("desc");

        // Fetch the updated data immediately
        await fetchRoomTypes();
      } else {
        // For edit operations, just fetch the data
        await fetchRoomTypes();
      }
      closeModal();
    } catch (error: any) {
      console.error("Error saving room type:", error);

      if (error.response && error.response.data && error.response.data.message) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error("Error saving room type. Please try again.");
      }
    }
  };

  // Delete modal management
  const openDeleteConfirmModal = (id: number) => {
    setSelectedRows([]); // Clear any multi-selection
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const openMultiDeleteConfirmModal = () => {
    setDeletingId(null); // Clear any single selection
    setIsDeleteConfirmOpen(true);
  };

  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  // Handle selected rows from GenericTable
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);

    // If rows are selected, show the delete button in the table header
    if (selectedIds.length > 0) {
      // The delete button is already shown by GenericTable
    }
  };

  // Handle form submission
  const handleFormSubmit = (e: React.FormEvent) => {
    console.log("Form submitted");
    e.preventDefault();
    // If editing, include the ID
    const submitData = {...formData};
    if (selectedRoomType?.id) {
      submitData.id = selectedRoomType.id;
    }

    // Ensure hotel_id is set
    if (!submitData.hotel_id && currentUserHotelId) {
      submitData.hotel_id = currentUserHotelId;
    }

    console.log("Calling handleSave with data:", submitData);
    handleSave(submitData);
  };

  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        // Multiple deletion
        let successCount = 0;
        let errorCount = 0;

        // Process each selected room type
        for (const id of selectedRows) {
          try {
            await userApi.deleteRoomType(id.toString());
            successCount++;
          } catch (error) {
            console.error(`Error deleting room type with ID ${id}:`, error);
            errorCount++;
          }
        }

        // Show appropriate toast messages
        if (successCount > 0) {
          toast.success(`Successfully deleted ${successCount} room type(s)`);
        }
        if (errorCount > 0) {
          toast.error(`Failed to delete ${errorCount} room type(s)`);
        }

        // Clear selection
        setSelectedRows([]);
      } else if (deletingId) {
        // Single deletion
        await userApi.deleteRoomType(deletingId.toString());
        toast.success("Room type deleted successfully");
      } else {
        return; // Nothing to delete
      }

      fetchRoomTypes();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Failed to delete room type(s)");
      console.error("Error deleting room type(s):", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Room Types | Admin Panel"
        description="Manage room types in the system"
      />
      <PageBreadcrumb pageTitle="Room Types" />

      <ComponentCard title="Room Type List">
        <TableControls
          search={search}
          onSearchChange={handleSearch}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          onAddClick={() => openModal("add")}
          addButtonLabel="Add Room Type"
        />
        <div className="w-full overflow-hidden" style={{ position: "relative" }}>
          <GenericTable
            data={paginatedRoomTypes}
            columns={[
            {
              header: "Name",
              accessor: "name",
              sortable: true
            },
            {
              header: "Description",
              accessor: "description",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.description || "-"}
                </div>
              )
            },
            {
              header: "Base Price",
              accessor: "base_rate",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.base_rate || "-"}
                </div>
              ),
              sortable: true
            },
            {
              header: "Max Adults",
              accessor: "max_adults",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.max_adults || "-"}
                </div>
              ),
              sortable: true
            },
            {
              header: "Max Children",
              accessor: "max_children",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.max_children || "-"}
                </div>
              ),
              sortable: true
            },
            {
              header: "Status",
              accessor: "status",
              cell: (item) => (
                <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                  item.status
                    ? "bg-green-100 text-green-800"
                    : "bg-red-100 text-red-800"
                }`}>
                  {item.status ? "Active" : "Inactive"}
                </span>
              )
            }
          ]}
          onEdit={(roomType) => openModal("edit", roomType)}
          onDelete={(id) => openDeleteConfirmModal(id)}
          startIndex={startingSerialNumber}
          onSort={handleSort}
          sortField={sortField}
          sortDirection={sortDirection}
          idField="id"
          showCheckboxes={true}
          onSelectedRowsChange={handleSelectedRowsChange}
          onDeleteSelected={openMultiDeleteConfirmModal}
               currentPage={currentPage}           
  itemsPerPage={itemsPerPage}  
        />
        </div>

        {totalItems > 0 && (
          <div className="mt-2">
            <Pagination
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              startingSerialNumber={startingSerialNumber}
            />
          </div>
        )}
      </ComponentCard>

      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Room Type" : "Add Room Type"}
        onSubmit={handleFormSubmit}
      >
        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* Name */}
          <div className="col-span-2">
            <Label>Name <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="name"
              value={formData.name || ''}
              onChange={(e) => setFormData({...formData, name: e.target.value})}
              error={!!errors.name}
              hint={errors.name || ""}
            />
          </div>

          {/* Description */}
          <div className="col-span-2">
            <Label>Description</Label>
            <textarea
              name="description"
              value={formData.description || ''}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              rows={3}
            />
          </div>

          {/* Base Price */}
          <div className="col-span-1">
            <Label>Base Price <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="number"
              name="base_rate"
              value={formData.base_rate?.toString() || '0'}
              onChange={(e) => setFormData({...formData, base_rate: parseFloat(e.target.value) || 0})}
              error={!!errors.base_rate}
              hint={errors.base_rate || ""}
            />
          </div>

          {/* Max Adults */}
          <div className="col-span-1">
            <Label>Max Adults <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="number"
              name="max_adults"
              value={formData.max_adults?.toString() || '2'}
              onChange={(e) => setFormData({...formData, max_adults: parseInt(e.target.value) || 1})}
              error={!!errors.max_adults}
              hint={errors.max_adults || ""}
            />
          </div>

          {/* Max Children */}
          <div className="col-span-1">
            <Label>Max Children <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="number"
              name="max_children"
              value={formData.max_children?.toString() || '1'}
              onChange={(e) => setFormData({...formData, max_children: parseInt(e.target.value) || 0})}
              error={!!errors.max_children}
              hint={errors.max_children || ""}
            />
          </div>

          {/* Status */}
          <div className="col-span-1">
            <Label>Status <span className="text-black dark:text-white">*</span></Label>
            <div className="flex items-center p-3 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600">
              <input
                type="checkbox"
                id="status"
                checked={formData.status || false}
                onChange={(e) => setFormData({...formData, status: e.target.checked})}
                className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <label htmlFor="status" className="ml-3 block text-base font-medium text-gray-900 dark:text-white">
                Active
              </label>
            </div>
          </div>

          {/* No need to send hotel_id anymore - it will be extracted from token in backend */}
        </div>
      </GenericAddEditModal>

      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        UserName={deletingId ? roomTypes.find(roomType => roomType.id === deletingId)?.name || "" : ""}
        selectedItems={selectedRows.length > 0 ?
          selectedRows.map(id => roomTypes.find(roomType => roomType.id === id)?.name || `Room Type #${id}`).filter(Boolean)
          : undefined}
      />
    </>
  );
};

export default RoomTypeComponent;
