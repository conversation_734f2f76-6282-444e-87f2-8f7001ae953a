import { useState, ChangeEvent, FormEvent } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { <PERSON>Eye, FiEyeOff } from "react-icons/fi";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import appConfig from "../../config/appConfig";
import { userApi } from "../../services/api";

interface FormData {
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  password: string;
  role_id: string;
  hotel_name: string;
}

export default function SignUpForm() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    first_name: "",
    last_name: "",
    phone: "",
    email: "",
    password: "",
    role_id: "2", // Default to Hotel Master (role_id: 2)
    hotel_name: "",
  });

  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validateField = (name: string, value: string) => {
    let error = "";

    switch (name) {
      case "first_name":
      case "last_name":
        if (!value.trim()) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name is required.`;
        } else if (!/^[A-Za-z]{3,}$/.test(value)) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name must contain at least 3 letters.`;
        }
        break;

      case "phone":
        if (!value.trim()) {
          error = "Phone number is required.";
        } else if (!/^[6-9]\d{9}$/.test(value)) {
          error = "Phone number must be a 10-digit number starting with 6-9.";
        }
        break;

      case "email":
        if (!value.trim()) {
          error = "Email is required.";
        } else if (!/^[\w-.]+@[\w-]+\.[a-z]{2,}$/.test(value)) {
          error = "Enter a valid email address.";
        }
        break;

      case "password":
        if (!value.trim()) {
          error = "Password is required.";
        } else if (value.length < 6) {
          error = "Password must be at least 6 characters.";
        }
        break;

      case "hotel_name":
        if (!value.trim()) {
          error = "Hotel name is required.";
        }
        break;

      default:
        break;
    }

    setErrors((prevErrors) => ({ ...prevErrors, [name]: error }));
    return error;
  };

  const handleChange = (
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    if (name === "phone" && value.length > 10) return;
    setFormData((prev) => ({ ...prev, [name]: value }));
    validateField(name, value);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    let newErrors: Partial<FormData> = {};
    Object.keys(formData).forEach((field) => {
      const error = validateField(field, formData[field as keyof FormData]);
      if (error) {
        newErrors[field as keyof FormData] = error;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      toast.error("Please fix the validation errors before submitting.");
      return;
    }

    try {
      await userApi.register(formData);
      toast.success("Registration successful as Hotel Master! You can now log in with your credentials.");
      setFormData({
        first_name: "",
        last_name: "",
        phone: "",
        email: "",
        password: "",
        role_id: "2",
        hotel_name: "",
      });
      setErrors({});
      navigate("/signin");
    } catch (err: any) {
      toast.error(
        err.response?.data?.message || "Signup failed. Please try again."
      );
    }
  };

  return (
    <>
      <div className="flex flex-col flex-1 w-full overflow-y-auto lg:w-1/2 no-scrollbar">
        <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto px-6 py-8">
          <div className="mb-8">
            <h1 className="mb-3 font-bold text-gray-800 text-3xl dark:text-white">
              Join <span className="text-blue-700 font-bold">{appConfig.appName}</span> Management
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Register to start managing your {appConfig.appName} properties
            </p>
          </div>
          <form onSubmit={handleSubmit} className="space-y-5 w-full">
              {/* Hotel Name Field */}
              <div>
              <Label>
                Hotel Name <span className="text-error-500">*</span>
              </Label>
              <Input
                type="text"
                name="hotel_name"
                value={formData.hotel_name}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                placeholder="Enter your hotel name"
              />
              {errors.hotel_name && (
                <p className="text-red-500 text-xs">{errors.hotel_name}</p>
              )}
            </div>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <Label>
                  First Name <span className="text-error-500">*</span>
                </Label>
                <Input
                  type="text"
                  name="first_name"
                  value={formData.first_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                />
                {errors.first_name && (
                  <p className="text-red-500 text-xs">{errors.first_name}</p>
                )}
              </div>
              <div>
                <Label>
                  Last Name <span className="text-error-500">*</span>
                </Label>
                <Input
                  type="text"
                  name="last_name"
                  value={formData.last_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                />
                {errors.last_name && (
                  <p className="text-red-500 text-xs">{errors.last_name}</p>
                )}
              </div>
            </div>
            <div>
              <Label>
                Phone <span className="text-error-500">*</span>
              </Label>
              <Input
                type="text"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
              />
              {errors.phone && (
                <p className="text-red-500 text-xs">{errors.phone}</p>
              )}
            </div>
            <div>
              <Label>
                Email <span className="text-error-500">*</span>
              </Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
              />
              {errors.email && (
                <p className="text-red-500 text-xs">{errors.email}</p>
              )}
            </div>
            <div>
              <Label>
                Password <span className="text-error-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute cursor-pointer right-4 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? <FiEye size={20} /> : <FiEyeOff size={20} />}
                </span>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs">{errors.password}</p>
              )}
            </div>

          

            <div className="p-4 bg-blue-50 rounded-md dark:bg-blue-900/20 border border-blue-100 dark:border-blue-800 shadow-sm">
              <p className="text-sm text-blue-700 dark:text-blue-400 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                Note: Your account will be registered as a {appConfig.appName} Master. You can log in immediately after registration.
              </p>
            </div>

            <button
              type="submit"
              className="w-full px-4 py-3 text-sm font-medium text-white bg-blue-700 hover:bg-blue-800 rounded-lg focus:ring-4 focus:outline-none focus:ring-blue-300 dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 transition-all duration-200 ease-in-out transform hover:scale-[1.02] shadow-md hover:shadow-lg"
            >
              Create Account
            </button>
          </form>
          <div className="mt-8 text-center">
            <p className="text-sm font-normal text-gray-600 dark:text-gray-300">
              Already have an account?{" "}
              <Link
                to="/signin"
                className="font-medium text-blue-700 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Sign In
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
