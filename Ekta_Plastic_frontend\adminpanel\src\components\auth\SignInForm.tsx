import { useState, ChangeEvent, FormEvent } from "react";
import { Link, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { <PERSON><PERSON>ye, FiEyeOff } from "react-icons/fi";
import Label from "../form/Label";
import Input from "../form/input/InputField";
import Button from "../ui/button/Button";
import { useAuth } from "../../context/AuthContext";
import appConfig from "../../config/appConfig";
import { userApi } from "../../services/api";

interface FormData {
  email: string;
  password: string;
}

export default function SignInForm() {
  const { storeTokenInLS } = useAuth();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
  });
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [showPassword, setShowPassword] = useState(false);

  const validateField = (name: string, value: string) => {
    let error = "";

    switch (name) {
      case "email":
        if (!value.trim()) {
          error = "Email is required.";
        } else if (!/^[\w-.]+@[\w-]+\.[a-z]{2,}$/.test(value)) {
          error = "Enter a valid email address.";
        }
        break;
      case "password":
        if (!value.trim()) {
          error = "Password is required.";
        } else if (value.length < 6) {
          error = "Password must be at least 6 characters.";
        }
        break;
      default:
        break;
    }

    setErrors((prevErrors) => ({ ...prevErrors, [name]: error }));
    return error;
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
    validateField(name, value);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    console.log("Form submission started");
    let newErrors: Partial<FormData> = {};
    Object.keys(formData).forEach((field) => {
      const error = validateField(field, formData[field as keyof FormData]);
      if (error) {
        newErrors[field as keyof FormData] = error;
      }
    });

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      toast.error("Please fix the validation errors before submitting.");
      // console.log("Validation errors:", newErrors);
      return;
    }
    console.log("Sending request to backend with data:", formData);
    try {
      console.log("Making login request using API service");
      const response = await userApi.login(formData);

      console.log("Response received:", response);
      console.log("Response data structure:", JSON.stringify(response.data, null, 2));

      // Display the success message directly from the backend response
      toast.success(response.data.message || "Login successful!");
      console.log("Login success message:", response.data.message);

      // Store token and navigate
      setFormData({
        email: "",
        password: "",
      });
      setErrors({});
      storeTokenInLS(response.data.token);
      navigate("/");
    } catch (err: any) {
      console.error("Error details:", err);

      toast.error(
        err.response?.data?.message || "Login failed. Please try again."
      );
    }
  };

  return (
    <>
      <div className="flex flex-col flex-1 w-full overflow-y-auto lg:w-1/2 no-scrollbar">
        <div className="flex flex-col justify-center flex-1 w-full max-w-md mx-auto px-6 py-8">
          <div className="mb-8">
            <h1 className="mb-3 font-bold text-gray-800 text-3xl dark:text-white">
              Welcome to <span className="text-blue-700 font-bold">{appConfig.appName}</span>
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Sign in to access your {appConfig.appName} dashboard
            </p>
          </div>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <Label>
                Email <span className="text-error-500">*</span>
              </Label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="<EMAIL>"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
              />
              {errors.email && (
                <p className="text-red-500 text-xs">{errors.email}</p>
              )}
            </div>
            <div>
              <Label>
                Password <span className="text-error-500">*</span>
              </Label>
              <div className="relative">
                <Input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  placeholder="Enter your password"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-200 focus:ring-opacity-50 dark:bg-gray-800 dark:border-gray-700 dark:text-white"
                />
                <span
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute cursor-pointer right-4 top-1/2 -translate-y-1/2 text-gray-500"
                >
                  {showPassword ? <FiEye size={20} /> : <FiEyeOff size={20} />}
                </span>
              </div>
              {errors.password && (
                <p className="text-red-500 text-xs">{errors.password}</p>
              )}
            </div>
            <div>
              <Button
                className="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-3 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 transition-all duration-200 ease-in-out transform hover:scale-[1.02] shadow-md hover:shadow-lg"
                type="submit"
                size="sm"
              >
                Sign in
              </Button>
            </div>
          </form>
          <div className="mt-8 text-center">
            <p className="text-sm font-normal text-gray-600 dark:text-gray-300">
              Don&apos;t have an account?{" "}
              <Link
                to="/signup"
                className="font-medium text-blue-700 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Sign Up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
