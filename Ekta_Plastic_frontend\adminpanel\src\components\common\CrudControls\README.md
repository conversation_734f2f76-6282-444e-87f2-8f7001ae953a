# CRUD Control Components

This directory contains reusable components for common CRUD operations in the admin panel.

## Available Components

### 1. TableControls

A complete table control component that combines search, per-page selection, and add button.

```jsx
import { TableControls } from "../common/CrudControls";

// Usage
<TableControls
  search={search}
  onSearchChange={handleSearch}
  itemsPerPage={itemsPerPage}
  onItemsPerPageChange={handleItemsPerPageChange}
  onAddClick={() => openModal("add")}
  addButtonLabel="Add Item"
  perPageOptions={[5, 10, 25, 50]} // Optional
  searchPlaceholder="Search..." // Optional
/>
```

### 2. GenericSearch

A standalone search input component.

```jsx
import { GenericSearch } from "../common/CrudControls";

// Usage
<GenericSearch
  value={search}
  onChange={handleSearch}
  placeholder="Search..." // Optional
/>
```

### 3. GenericPerPageSelector

A standalone component for selecting the number of items per page.

```jsx
import { GenericPerPageSelector } from "../common/CrudControls";

// Usage
<GenericPerPageSelector
  itemsPerPage={itemsPerPage}
  onItemsPerPageChange={handleItemsPerPageChange}
  options={[5, 10, 25, 50]} // Optional
/>
```

### 4. GenericAddButton

A standalone add button component.

```jsx
import { GenericAddButton } from "../common/CrudControls";

// Usage
<GenericAddButton
  onClick={() => openModal("add")}
  label="Add Item" // Optional, defaults to "Add"
/>
```

## Implementation Example

Here's a complete example of how to use these components in a CRUD component:

```jsx
import React, { useState } from 'react';
import { TableControls } from "../common/CrudControls";

const MyComponent = () => {
  const [search, setSearch] = useState("");
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  
  const handleSearch = (e) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };
  
  const handleItemsPerPageChange = (value) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };
  
  const openAddModal = () => {
    // Your code to open the add modal
  };
  
  return (
    <div>
      <TableControls
        search={search}
        onSearchChange={handleSearch}
        itemsPerPage={itemsPerPage}
        onItemsPerPageChange={handleItemsPerPageChange}
        onAddClick={openAddModal}
        addButtonLabel="Add New Item"
      />
      
      {/* Your table component here */}
    </div>
  );
};
```
