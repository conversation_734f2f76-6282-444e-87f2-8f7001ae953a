import React from "react";
import { Link } from "react-router-dom";
import { AlertCircle, Star } from "lucide-react";

const PremiumRequired = () => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[70vh] p-6 text-center">
      <div className="w-20 h-20 flex items-center justify-center bg-gray-100 rounded-full mb-6">
        <AlertCircle size={40} className="text-gray-500" />
      </div>

      <h1 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
        Premium Feature
      </h1>

      <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md">
        This feature is only available to premium users. Please upgrade your account to access all features.
      </p>

      <div className="flex flex-row gap-4">
        <Link
          to="/profile"
          className="px-6 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
        >
          Go to Profile
        </Link>

        <button
          className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center"
        >
          <Star size={16} className="mr-2" />
          <span>Upgrade to Premium</span>
        </button>
      </div>
    </div>
  );
};

export default PremiumRequired;
