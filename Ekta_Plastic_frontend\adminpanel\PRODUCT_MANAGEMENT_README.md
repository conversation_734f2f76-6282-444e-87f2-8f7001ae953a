# Ekta Plastic Product Management System

## Overview
This is a complete product management system built for Ekta Plastic, featuring a clean and intuitive admin panel for managing products, categories, and images.

## Features

### ?? Core Functionality
- **Product Management**: Add, edit, delete, and view products with detailed information
- **Category Management**: Organize products into categories
- **Image Management**: Upload and manage product images
- **Search & Filter**: Advanced search functionality across all modules
- **Pagination**: Efficient data loading with customizable page sizes
- **Sorting**: Sort data by any column in ascending/descending order
- **Bulk Operations**: Select multiple items for bulk delete operations

### ?? Technical Features
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile
- **Real-time Updates**: Instant feedback with toast notifications
- **Form Validation**: Client-side validation with error handling
- **Loading States**: Proper loading indicators for better UX
- **Error Handling**: Comprehensive error handling with user-friendly messages
- **TypeScript**: Fully typed for better development experience

## Pages & Routes

### ?? Dashboard (`/`)
- Product statistics overview
- Quick action buttons
- Getting started guide
- Real-time data from API

### ?? Product Management (`/products`)
- Complete CRUD operations for products
- Fields: Category, Subcategory, Price, Old Price, Stock, Description, Specification, Sizes, Images, Bestseller status
- Advanced search and filtering
- Product details view modal
- Bulk delete functionality

### ??? Category Management (`/product-categories`)
- Simple category management
- Add, edit, delete categories
- Search functionality
- Used as reference in product creation

### ??? Image Management (`/product-images`)
- Upload and manage product images
- Link images to specific products
- Image preview functionality
- Active/inactive status management

### ?? User Profile (`/profile`)
- User profile management
- Change password functionality

## API Integration

### Endpoints Used
```
Product Categories:
- GET /product-categories (Get all categories)
- POST /product-categories (Create category)
- PUT /product-categories/:id (Update category)
- DELETE /product-categories/:id (Delete category)

Products:
- GET /products (Get all products)
- POST /products (Create product)
- PUT /products/:id (Update product)
- DELETE /products/:id (Delete product)

Product Images:
- GET /product-images (Get all images)
- GET /product-images/product/:product_id (Get images by product)
- POST /product-images (Create image)
- PUT /product-images/:id (Update image)
- DELETE /product-images/:id (Delete image)
```

## Components Structure

```
src/
??? components/
?   ??? Product/
?   ?   ??? ProductManagement.tsx
?   ?   ??? ProductCategoryManagement.tsx
?   ?   ??? ProductImagesManagement.tsx
?   ?   ??? index.ts
?   ??? Dashboard/
?   ?   ??? ProductDashboard.tsx
?   ??? [other existing components]
??? services/
?   ??? api.ts (Updated with product endpoints)
??? layout/
?   ??? AppSidebar.tsx (Simplified navigation)
??? App.tsx (Updated routes)
```

## Key Features Implemented

### ?? Search Functionality
- Real-time search across relevant fields
- Case-insensitive search
- Search in nested fields (e.g., category names in products)

### ?? Pagination
- Configurable items per page (10, 15, 25, 50)
- Page navigation with ellipsis for large datasets
- Shows current page info and total items

### ?? Sorting
- Click column headers to sort
- Visual indicators for sort direction
- Supports text, number, and date sorting

### ? Form Validation
- Required field validation
- Custom error messages
- Real-time validation feedback
- Prevents invalid data submission

### ?? UI/UX Features
- Consistent design language
- Loading states and skeletons
- Toast notifications for actions
- Confirmation dialogs for destructive actions
- Responsive tables with horizontal scroll
- Dark mode support

## Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Configure API**
   - Update `src/config/appConfig.ts` if needed
   - Ensure backend API is running on `http://localhost:3000`

3. **Run Development Server**
   ```bash
   npm run dev
   ```

4. **Access the Application**
   - Open `http://localhost:5173` in your browser
   - Login with your credentials
   - Navigate through the product management features

## Navigation Structure

The sidebar has been simplified to show only essential features:
- **Dashboard**: Overview and statistics
- **Product Management**: 
  - Product Categories
  - Products  
  - Product Images
- **User Profile**: Profile and settings

## Data Models

### Product Category
```typescript
{
  id: number;
  name: string;
  isDeleted: boolean;
  createdAt: string;
}
```

### Product
```typescript
{
  id: number;
  category_id: number;
  subcategory: string;
  image: string;
  price: number;
  oldPrice: number;
  badge: string;
  description: string;
  specification: string;
  stock: number;
  count: number;
  sizes: string[];
  isbestsellerproduct: boolean;
  isDeleted: boolean;
  createdAt: string;
}
```

### Product Image
```typescript
{
  id: number;
  product_id: number;
  image_url: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
```

## Notes
- All components follow the existing design patterns from the Room Management system
- Error handling includes both client-side validation and server error responses
- The system is fully responsive and works on all device sizes
- Toast notifications provide immediate feedback for all user actions
- All forms include proper validation and error states
