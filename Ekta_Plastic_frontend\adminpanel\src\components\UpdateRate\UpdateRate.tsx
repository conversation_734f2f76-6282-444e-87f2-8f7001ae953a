import React, { useState, useEffect } from "react";
import { X } from "lucide-react";
import { userApi } from "../../services/api";

interface UpdateRatePopupProps {
  isOpen: boolean;
  onClose: () => void;
  selectedDates: Array<{ room_type_id: string; date: Date }>;
  room_type_id: string;
  roomTypes: any[];
  onRateUpdated: () => void;
  onDateRemove: (dateToRemove: Date) => void;
}

const UpdateRatePopup: React.FC<UpdateRatePopupProps> = ({
  isOpen,
  onClose,
  selectedDates,
  room_type_id,
  roomTypes,
  onRateUpdated,
  onDateRemove,
}) => {
  const [newRate, setNewRate] = useState<number>(0);
  const [updating, setUpdating] = useState<boolean>(false);
  const [error, setError] = useState<string>("");
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDragging, setIsDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  // Get room type name
  const getRoomTypeName = () => {
    if (!room_type_id || selectedDates.length === 0) {
      return "Select a room type";
    }
    const roomType = roomTypes.find((rt) => rt.id === room_type_id);
    return roomType ? roomType.name : "Unknown";
  };

  // Format date for display
  const formatDate = (date: Date) => {
    return `${date.getDate().toString().padStart(2, "0")}-${(
      date.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}-${date.getFullYear()}`;
  };

  // Set initial rate based on the room type
  useEffect(() => {
    if (room_type_id && roomTypes.length > 0) {
      const roomType = roomTypes.find((rt) => rt.id === room_type_id);
      if (roomType) {
        setNewRate(roomType.base_rate);
      }
    }
  }, [room_type_id, roomTypes]);

  // Reset position when popup opens
  useEffect(() => {
    if (isOpen) {
      setPosition({ x: 0, y: 0 });
    }
  }, [isOpen]);

  // Format for API (DD/MM/YYYY)
  const formatDateForAPI = (date: Date) => {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const handleRateUpdate = async () => {
    try {
      setUpdating(true);
      setError("");

      const updateData = {
        room_type_id,
        dates: selectedDates.map((sd) => formatDateForAPI(sd.date)),
        base_rate: newRate,
      };

      await userApi.updateRoomRates(updateData);
      onRateUpdated();
      onClose();
    } catch (error) {
      setError("Failed to update rates. Please try again.");
    } finally {
      setUpdating(false);
    }
  };

  const handleRemoveDate = (dateToRemove: Date) => {
    onDateRemove(dateToRemove);
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (e.target instanceof HTMLElement && e.target.closest(".popup-header")) {
      setIsDragging(true);
      setDragStart({
        x: e.clientX - position.x,
        y: e.clientY - position.y,
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 pointer-events-none z-50"
      style={{ backgroundColor: "transparent" }}
    >
      <div
      className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-88 pointer-events-auto absolute transform -translate-x-1/2 border border-gray-300 dark:border-gray-600"
        style={{
          left: `calc(50% + ${position.x}px)`,
          top: `${Math.max(0, 20 + position.y)}px`,
          cursor: isDragging ? "grabbing" : "auto",
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <div className="popup-header p-4 cursor-grab active:cursor-grabbing border-b dark:border-gray-700">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Update Room Rate
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
            >
              <X size={16} />
            </button>
          </div>
        </div>

        <div className="p-4">
          <div className="flex justify-between items-center mb-4">
            <div className="w-1/2 pr-2">
              <label
                htmlFor="newRate"
                className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1"
              >
                New Base Rate
              </label>
              <input
                type="number"
                id="newRate"
                value={newRate}
                onChange={(e) => setNewRate(Number(e.target.value))}
                className="w-full px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white text-sm"
                min="0"
                step="1"
              />
              {error && (
                <p className="mt-1 text-red-600 dark:text-red-400 text-xs">
                  {error}
                </p>
              )}
            </div>
            <div className="w-1/2 pl-2">
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Room Type
              </label>
              <p className="text-sm text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 px-3 py-1.5 rounded-md">
                {getRoomTypeName()}
              </p>
            </div>
          </div>

          <div className="mb-3">
            <div className="mt-1 max-h-[100px] overflow-y-auto bg-gray-50 dark:bg-gray-700 rounded p-2">
              <ul className="grid grid-cols-3 gap-2">
                {selectedDates
                  .filter((sd) => sd.room_type_id === room_type_id)
                  .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime()) //sort by date
                  .map((dateObj, index) => (
                    <li
                      key={index}
                      className="flex items-center justify-between bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-md shadow-sm border dark:border-gray-600"
                      style={{ width: "92px" }} // Reduced width for each date box
                    >
                      <span className="text-xs whitespace-nowrap">
                        {formatDate(dateObj.date)}
                      </span>{" "}
                      {/* Smaller font size */}
                      <button
                        onClick={() => handleRemoveDate(dateObj.date)}
                        className="ml-2 text-blue-500 hover:text-blue-700"
                      >
                        <X size={10} /> {/* Smaller "X" icon */}
                      </button>
                    </li>
                  ))}
              </ul>
            </div>
          </div>

          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="flex-1 py-1.5 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none"
            >
              Cancel
            </button>
            <button
              onClick={handleRateUpdate}
              disabled={updating || selectedDates.length === 0}
              className="flex-1 py-1.5 px-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {updating ? "Updating..." : "Update Rates"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UpdateRatePopup;
