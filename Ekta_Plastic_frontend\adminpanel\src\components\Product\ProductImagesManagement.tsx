import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../common/PageBreadCrumb";
import ComponentCard from "../common/ComponentCard";
import PageMeta from "../common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface Product {
  id: number;
  subcategory: string;
  price: number;
}

interface ProductImage {
  id?: number;
  product_id: number;
  image_url: string;
  isActive?: boolean;
  createdAt?: string;
  updatedAt?: string;
  sr_no?: number;
  Product?: Product;
}

const ProductImagesManagement = () => {
  const [productImages, setProductImages] = useState<ProductImage[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedProductImage, setSelectedProductImage] = useState<ProductImage | null>(null);
  const [formData, setFormData] = useState<ProductImage>({
    product_id: 0,
    image_url: "",
    isActive: true,
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [isMultiDeleteModalOpen, setIsMultiDeleteModalOpen] = useState<boolean>(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Fetch product images and products on component mount
  useEffect(() => {
    fetchProducts();
    fetchProductImages();
  }, [currentPage, itemsPerPage, search, sortField, sortDirection]);

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      const response = await userApi.getAllProducts();
      setProducts(response.data || []);
    } catch (error: any) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    }
  };

  // Fetch product images from API
  const fetchProductImages = async () => {
    try {
      setIsLoading(true);
      const response = await userApi.getAllProductImages();
      
      let filteredImages = response.data || [];

      // Filter by search
      if (search.trim()) {
        filteredImages = filteredImages.filter((image: ProductImage) =>
          image.image_url.toLowerCase().includes(search.toLowerCase()) ||
          (image.Product?.subcategory || "").toLowerCase().includes(search.toLowerCase())
        );
      }

      // Sort images
      filteredImages.sort((a: ProductImage, b: ProductImage) => {
        let aValue: any = a[sortField as keyof ProductImage];
        let bValue: any = b[sortField as keyof ProductImage];
        
        // Handle nested product subcategory sorting
        if (sortField === "product_subcategory") {
          aValue = a.Product?.subcategory || "";
          bValue = b.Product?.subcategory || "";
        }
        
        if (typeof aValue === "string" && typeof bValue === "string") {
          return sortDirection === "asc" 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        if (aValue instanceof Date && bValue instanceof Date) {
          return sortDirection === "asc" 
            ? aValue.getTime() - bValue.getTime()
            : bValue.getTime() - aValue.getTime();
        }
        
        return 0;
      });

      setTotalItems(filteredImages.length);
      
      // Paginate
      const startIndex = (currentPage - 1) * itemsPerPage;
      const paginatedImages = filteredImages.slice(startIndex, startIndex + itemsPerPage);
      
      // Add serial numbers
      const imagesWithSerial = paginatedImages.map((image: ProductImage, index: number) => ({
        ...image,
        sr_no: startIndex + index + 1,
      }));

      setProductImages(imagesWithSerial);
    } catch (error: any) {
      console.error("Error fetching product images:", error);
      toast.error("Failed to fetch product images");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
    setCurrentPage(1);
  };

  // Open modal for add/edit
  const openModal = (type: "add" | "edit", productImage?: ProductImage) => {
    setModalType(type);
    setSelectedProductImage(productImage || null);
    setFormData(productImage ? { ...productImage } : {
      product_id: 0,
      image_url: "",
      isActive: true,
    });
    setErrors({});
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setModalType("");
    setSelectedProductImage(null);
    setFormData({
      product_id: 0,
      image_url: "",
      isActive: true,
    });
    setErrors({});
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    
    if (type === "number") {
      processedValue = parseInt(value) || 0;
    } else if (type === "checkbox") {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({ ...prev, [name]: processedValue }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.product_id || formData.product_id === 0) {
      newErrors.product_id = "Product is required";
    }
    if (!formData.image_url.trim()) {
      newErrors.image_url = "Image URL is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (selectedProductImage) {
        // Update existing product image
        await userApi.editProductImage(selectedProductImage.id!, formData);
        toast.success("Product image updated successfully");
      } else {
        // Create new product image
        await userApi.addProductImage(formData);
        toast.success("Product image created successfully");
      }

      closeModal();
      fetchProductImages();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error(selectedProductImage ? "Failed to update product image" : "Failed to create product image");
      }
      console.error("Error saving product image:", error);
    }
  };

  // Open delete confirmation modal
  const openDeleteConfirmModal = (id: number) => {
    setDeletingId(id);
    setIsDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteConfirmModal = () => {
    setIsDeleteModalOpen(false);
    setDeletingId(null);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deletingId) return;

    try {
      await userApi.deleteProductImage(deletingId);
      toast.success("Product image deleted successfully");
      closeDeleteConfirmModal();
      fetchProductImages();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to delete product image");
      }
      console.error("Error deleting product image:", error);
    }
  };

  // Handle selected rows change
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  // Open multi-delete confirmation modal
  const openMultiDeleteConfirmModal = () => {
    if (selectedRows.length === 0) {
      toast.warning("Please select product images to delete");
      return;
    }
    setIsMultiDeleteModalOpen(true);
  };

  // Close multi-delete confirmation modal
  const closeMultiDeleteConfirmModal = () => {
    setIsMultiDeleteModalOpen(false);
  };

  // Handle multi-delete
  const handleMultiDelete = async () => {
    try {
      await Promise.all(
        selectedRows.map(id => userApi.deleteProductImage(id))
      );
      toast.success(`${selectedRows.length} product images deleted successfully`);
      setSelectedRows([]);
      closeMultiDeleteConfirmModal();
      fetchProductImages();
    } catch (error: any) {
      toast.error("Failed to delete some product images");
      console.error("Error deleting product images:", error);
    }
  };

  return (
    <>
      <PageMeta title="Product Images Management" />
      <PageBreadcrumb title="Product Images Management" />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <ComponentCard title="Product Images">
          <TableControls
            search={search}
            onSearchChange={handleSearch}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            onAddClick={() => openModal("add")}
            addButtonLabel="Add Product Image"
          />
          
          <div className="w-full overflow-hidden" style={{ position: "relative" }}>
            <GenericTable
              data={productImages}
              columns={[
                {
                  header: "Product",
                  accessor: "product_subcategory",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.Product?.subcategory || "-"}
                    </div>
                  ),
                },
                {
                  header: "Image",
                  accessor: "image_url",
                  cell: (item) => (
                    <div className="flex items-center">
                      <img 
                        src={item.image_url} 
                        alt="Product" 
                        className="w-12 h-12 object-cover rounded-md"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "/placeholder-image.png";
                        }}
                      />
                    </div>
                  ),
                },
                {
                  header: "Image URL",
                  accessor: "image_url",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300 max-w-xs truncate">
                      {item.image_url}
                    </div>
                  ),
                },
                {
                  header: "Status",
                  accessor: "isActive",
                  cell: (item) => (
                    <div className="text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        item.isActive 
                          ? "bg-green-100 text-green-800" 
                          : "bg-red-100 text-red-800"
                      }`}>
                        {item.isActive ? "Active" : "Inactive"}
                      </span>
                    </div>
                  ),
                },
                {
                  header: "Created At",
                  accessor: "createdAt",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "-"}
                    </div>
                  ),
                },
              ]}
              onEdit={(productImage) => openModal("edit", productImage)}
              onDelete={(id) => openDeleteConfirmModal(id)}
              startIndex={startingSerialNumber}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              idField="id"
              showCheckboxes={true}
              onSelectedRowsChange={handleSelectedRowsChange}
              onDeleteSelected={openMultiDeleteConfirmModal}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
            />
          </div>

          {totalItems > 0 && (
            <div className="mt-2">
              <Pagination
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                startingSerialNumber={startingSerialNumber}
              />
            </div>
          )}
        </ComponentCard>
      )}

      {/* Add/Edit Modal */}
      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Product Image" : "Add Product Image"}
        onSubmit={handleSubmit}
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="product_id">Product *</Label>
            <select
              id="product_id"
              name="product_id"
              value={formData.product_id}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={0}>Select Product</option>
              {products.map((product) => (
                <option key={product.id} value={product.id}>
                  {product.subcategory} - ?{product.price}
                </option>
              ))}
            </select>
            {errors.product_id && (
              <p className="text-red-500 text-sm mt-1">{errors.product_id}</p>
            )}
          </div>

          <div>
            <Label htmlFor="image_url">Image URL *</Label>
            <Input
              id="image_url"
              name="image_url"
              type="text"
              value={formData.image_url}
              onChange={handleInputChange}
              placeholder="Enter image URL"
              error={errors.image_url}
            />
          </div>

          {formData.image_url && (
            <div>
              <Label>Image Preview</Label>
              <div className="mt-2">
                <img
                  src={formData.image_url}
                  alt="Preview"
                  className="w-32 h-32 object-cover rounded-md border"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = "/placeholder-image.png";
                  }}
                />
              </div>
            </div>
          )}

          <div className="flex items-center">
            <input
              id="isActive"
              name="isActive"
              type="checkbox"
              checked={formData.isActive}
              onChange={handleInputChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <Label htmlFor="isActive" className="ml-2">
              Active
            </Label>
          </div>
        </div>
      </GenericAddEditModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        title="Delete Product Image"
        message="Are you sure you want to delete this product image? This action cannot be undone."
      />

      {/* Multi-Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isMultiDeleteModalOpen}
        onClose={closeMultiDeleteConfirmModal}
        onConfirm={handleMultiDelete}
        title="Delete Product Images"
        message={`Are you sure you want to delete ${selectedRows.length} selected product images? This action cannot be undone.`}
      />
    </>
  );
};

export default ProductImagesManagement;
