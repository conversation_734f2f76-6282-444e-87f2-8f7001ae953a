import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SignIn from "./pages/AuthPages/SignIn";

import NotFound from "./pages/OtherPage/NotFound";
import UserProfiles from "./pages/UserProfiles";
import ChangePassword from "./pages/ChangePassword";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import Home from "./pages/Dashboard/Home";
import { CustomerDashboard } from "./components/Customer/CustomerDashboard";
import { ManagerDashboard } from "./components/Manager/ManagerDashboard";
import PremiumRoute from "./PremiumRoute";
import { UserManagement } from "./components/HotelMaster/UserManagement";
import IconOptions from "./components/IconOptions";
import AdminDashboard from "./components/AdminDashboard/AdminDashboard";
import { Amenities } from "./components/Amenities/Amenities";
import { RoomTypeComponent } from "./components/RoomType/RoomType";
import RoomManagement from "./components/Room/RoomManagement";
import RoomAddOns from "./components/Room/RoomAddOns";
import RoomRateComponent from "./components/RoomRate/RoomRate";
import { Booking } from "./components/Booking/Booking";
import AddBooking from "./components/AddBooking/AddBooking";
import { SidebarProvider } from "./context/SidebarContext";
import CustomerManagement from "./components/Customer_Manangement/Customer_Manangement";
import ProductManagement from "./components/Product/ProductManagement";
import ProductCategoryManagement from "./components/Product/ProductCategoryManagement";
import ProductImagesManagement from "./components/Product/ProductImagesManagement";

export default function App() {
  // const { isLoggedIn } = useAuth();
  return (
    <>
      <Router>
        <ScrollToTop />
        <SidebarProvider>
          <Routes>
            {/* Auth Layout */}
            <Route path="/signin" element={<SignIn />} />
           

            {/* All routes are wrapped in AppLayout for consistent UI */}
            <Route element={<AppLayout />}>
              {/* Then we apply PremiumRoute for access control */}
              <Route element={<PremiumRoute />}>
                {/* Non-premium users can access these routes */}
                <Route path="/profile" element={<UserProfiles />} />
                <Route path="/change-password" element={<ChangePassword />} />
                {/* Only premium users can access these routes */}
                <Route index path="/" element={<Home />} />
                <Route path="/customer" element={<CustomerDashboard />} />
                <Route path="/hotelmaster" element={<UserManagement />} />
                <Route path="/manager" element={<ManagerDashboard />} />
                <Route path="/admindashboard" element={<AdminDashboard />} />
                <Route path="/amenities" element={<Amenities />} />
                <Route path="/room-types" element={<RoomTypeComponent />} />
                <Route path="/rooms" element={<RoomManagement />} />
                <Route path="/room-addons" element={<RoomAddOns />} />a
                <Route path="/room-rates" element={<RoomRateComponent />} />
                <Route path="/icons" element={<IconOptions />} />
                <Route path="/bookings" element={<Booking />} />
                <Route path="/add-booking" element={<AddBooking />} />
                <Route
                  path="/customer-Manager"
                  element={<CustomerManagement />}
                />
                <Route path="/products" element={<ProductManagement />} />
                <Route path="/product-categories" element={<ProductCategoryManagement />} />
                <Route path="/product-images" element={<ProductImagesManagement />} />
                {/* <Route path="/blank" element={<Blank />} /> */}
              </Route>
            </Route>
            <Route path="*" element={<NotFound />} />

            {/* Fallback Route */}
          </Routes>
        </SidebarProvider>
      </Router>
    </>
  );
}
