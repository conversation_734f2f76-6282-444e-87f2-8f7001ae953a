import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import SignIn from "./pages/AuthPages/SignIn";
import NotFound from "./pages/OtherPage/NotFound";
import UserProfiles from "./pages/UserProfiles";
import ChangePassword from "./pages/ChangePassword";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import ProductDashboard from "./components/Dashboard/ProductDashboard";
import PremiumRoute from "./PremiumRoute";
import { SidebarProvider } from "./context/SidebarContext";
import ProductManagement from "./components/Product/ProductManagement";
import ProductCategoryManagement from "./components/Product/ProductCategoryManagement";
import ProductImagesManagement from "./components/Product/ProductImagesManagement";

export default function App() {
  // const { isLoggedIn } = useAuth();
  return (
    <>
      <Router>
        <ScrollToTop />
        <SidebarProvider>
          <Routes>
            {/* Auth Layout */}
            <Route path="/signin" element={<SignIn />} />
           

            {/* All routes are wrapped in AppLayout for consistent UI */}
            <Route element={<AppLayout />}>
              {/* Then we apply PremiumRoute for access control */}
              <Route element={<PremiumRoute />}>
                {/* Dashboard and User Profile routes */}
                <Route index path="/" element={<ProductDashboard />} />
                <Route path="/profile" element={<UserProfiles />} />
                <Route path="/change-password" element={<ChangePassword />} />

                {/* Product Management routes */}
                <Route path="/products" element={<ProductManagement />} />
                <Route path="/product-categories" element={<ProductCategoryManagement />} />
                <Route path="/product-images" element={<ProductImagesManagement />} />
              </Route>
            </Route>
            <Route path="*" element={<NotFound />} />

            {/* Fallback Route */}
          </Routes>
        </SidebarProvider>
      </Router>
    </>
  );
}
