import { BrowserRouter as Router, Routes, Route, Navigate, Outlet } from "react-router-dom";
import SignIn from "./pages/AuthPages/SignIn";
import NotFound from "./pages/OtherPage/NotFound";
import UserProfiles from "./pages/UserProfiles";
import ChangePassword from "./pages/ChangePassword";
import AppLayout from "./layout/AppLayout";
import { ScrollToTop } from "./components/common/ScrollToTop";
import ProductDashboard from "./components/Dashboard/ProductDashboard";
import { SidebarProvider } from "./context/SidebarContext";
import ProductManagement from "./components/Product/ProductManagement";
import ProductCategoryManagement from "./components/Product/ProductCategoryManagement";
import ProductImagesManagement from "./components/Product/ProductImagesManagement";
import { useAuth } from "./context/AuthContext";

// Simple Protected Route Component
const ProtectedRoute = () => {
  const { isLoggedIn, isLoading } = useAuth();

  // Show loading while checking auth status
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // If not logged in, redirect to signin
  if (!isLoggedIn) {
    return <Navigate to="/signin" replace />;
  }

  // If logged in, allow access
  return <Outlet />;
};

export default function App() {
  // const { isLoggedIn } = useAuth();
  return (
    <>
      <Router>
        <ScrollToTop />
        <SidebarProvider>
          <Routes>
            {/* Auth Layout */}
            <Route path="/signin" element={<SignIn />} />

            {/* All routes are wrapped in AppLayout for consistent UI */}
            <Route element={<AppLayout />}>
              {/* Apply simple authentication check */}
              <Route element={<ProtectedRoute />}>
                {/* Dashboard and User Profile routes */}
                <Route index path="/" element={<ProductDashboard />} />
                <Route path="/profile" element={<UserProfiles />} />
                <Route path="/change-password" element={<ChangePassword />} />

                {/* Product Management routes */}
                <Route path="/products" element={<ProductManagement />} />
                <Route path="/product-categories" element={<ProductCategoryManagement />} />
                <Route path="/product-images" element={<ProductImagesManagement />} />
              </Route>
            </Route>
            <Route path="*" element={<NotFound />} />

            {/* Fallback Route */}
          </Routes>
        </SidebarProvider>
      </Router>
    </>
  );
}
