import React from 'react';

interface GenericAddButtonProps {
  onClick: () => void;
  label?: string;
  className?: string;
}

const GenericAddButton: React.FC<GenericAddButtonProps> = ({
  onClick,
  label = "Add",
  className = ""
}) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`px-4 py-1.5 bg-blue-500 text-white text-sm rounded-md hover:bg-blue-600 flex items-center ${className}`}
    >
      <svg
        xmlns="http://www.w3.org/2000/svg"
        className="h-5 w-5 mr-1"
        fill="none"
        viewBox="0 0 24 24"
        stroke="currentColor"
      >
        <path
          strokeLinecap="round"
          strokeLinejoin="round"
          strokeWidth={2}
          d="M12 4v16m8-8H4"
        />
      </svg>
      {label}
    </button>
  );
};

export default GenericAddButton;
