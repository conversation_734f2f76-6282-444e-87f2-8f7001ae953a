import React, { useState, useEffect, useCallback } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import { CalenderIcon } from "../../icons";
import { userApi } from "../../services/api";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useLocation, useNavigate } from "react-router-dom";
import { debounce } from "lodash";
// Define interfaces for our component
interface RoomNumberOption {
  id: number;
  room_number: string;
}

interface RoomTypeOption {
  id: number;
  name: string;
  description?: string;
  available_count: number;
  base_rate?: number;
  max_adults?: number;
  max_children?: number;
}


interface RatePlanOption {
  id: number;
  name: string;
  price: number;
  room_type_id: number;
}

interface CustomerInfo {
  id?: number;
  name: string;
  phone: string;
  email: string;
}

interface RoomDetail {
  room_type: string;
  room_type_id?: number;
  rate_plan: string;
  rate_plan_id?: number;
  rooms: number;
  guest_count: { adult: number; child: number }[]; // <-- now an array
  rate: number[];
  room_numbers: string[];
}

interface BookingFormData {
  check_in: string;
  check_out: string;
  nights: number;
  booking_source: string;
  booking_type: string;
  pay_at_hotel: boolean;
  room_details: RoomDetail[];
  returning_guest: boolean;
  guest_info: CustomerInfo;
}
type RoomBookingEntry = {
  room_number: string | number;
  room_type: string;
  rate: number;
  adults: number;
  children: number;
};


const AddBooking: React.FC = () => {
  // State for available room types, room numbers, and rate plans
  const [availableRoomTypes, setAvailableRoomTypes] = useState<
    RoomTypeOption[]
  >([]);
  const [availableRoomNumbers, setAvailableRoomNumbers] = useState<{
    [roomTypeId: number]: RoomNumberOption[];
  }>({});
  const [availableRatePlans, setAvailableRatePlans] = useState<{
    [roomTypeId: number]: RatePlanOption[];
  }>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [selectedRoomNumbers, setSelectedRoomNumbers] = useState<{
    [roomIndex: number]: string[];
  }>({});

  // Add a state for the returning guest phone input
  const [returningphone, setReturningphone] = useState("");

  const today = new Date();
  const todayStr = today.toISOString().split("T")[0];

  // Form data state
  const [formData, setFormData] = useState<BookingFormData>({
    check_in: todayStr, // set current date as default
    check_out: todayStr, // set current date as default
    nights: 0,
    booking_source: "Booking Engine",
    booking_type: "",
    pay_at_hotel: true,
    room_details: [
      {
        room_type: "",
        room_type_id: undefined,
        rate_plan: "Standard Rate",
        rate_plan_id: undefined,
        rooms: 1,
        guest_count: [{ adult: 1, child: 0 }], // <-- array
        rate: 0,
        room_numbers: [],
      },
    ],
    returning_guest: false,
    guest_info: { name: "", phone: "", email: "" },
  });

  const location = useLocation();
  const navigate = useNavigate();

  // Track edit mode and booking id
  const [editMode, setEditMode] = useState(false);
  const [editBookingId, setEditBookingId] = useState<number | null>(null);
  // Add this state for field-level errors
  const [roomFieldErrors, setRoomFieldErrors] = useState<{
    [roomIdx: number]: { [field: string]: string };
  }>({});

  // Add this state for guest info field errors
  const [guestFieldErrors, setGuestFieldErrors] = useState<{
    [field: string]: string;
  }>({});

  // Add this state for booking info field errors
  const [bookingFieldErrors, setBookingFieldErrors] = useState<{
    [field: string]: string;
  }>({});
  // Add a flag to ensure prefill runs only once
  const [prefilled, setPrefilled] = useState(false);
  const [totalPrice, setTotalPrice] = useState("0.00");

  useEffect(() => {
    const fetchTotal = async () => {
      const total = await calculateTotalPrice(formData);
      setTotalPrice(total); // This must set a string like "2000.00"
      console.log("TOTALPRICE", totalPrice);
    };

    fetchTotal();
  }, [formData.check_in, formData.check_out, formData.room_details]);

  // Fetch rates for each room type and calculate total
  const calculateTotalPrice = async (formData) => {
    let total = 0;

    for (const detail of formData.room_details) {
      const { room_type, room_numbers } = detail;

      if (!room_type || !formData.check_in || !formData.check_out) continue;

      try {
        const res = await userApi.getRoomRatesByName(
          room_type,
          formData.check_in,
          formData.check_out
        );
        console.log("res", res);
        const dayRates = await res.data; // Expected: [{date: "...", rate: "1000"}, ...]
        console.log("DayRates", dayRates);

        const roomTotal = dayRates.reduce(
          (sum, day) => sum + Number(day.rate),
          0
        );
        console.log("roomTotal", roomTotal);
        total += roomTotal * room_numbers.length;
        console.log("Total", total);
      } catch (error) {
        console.error("Error fetching rates:", error);
      }
    }

    return total.toFixed(2);
  };

  // Prefill logic: Only run when both editBooking and availableRoomTypes are ready
  useEffect(() => {
    if (
      location.state?.editBooking &&
      availableRoomTypes.length > 0 &&
      !prefilled
    ) {
      const { booking, customer, room_details } = location.state.editBooking;
      setEditMode(true);
      setEditBookingId(booking.id);

      // Map room details from backend to frontend format
      const roomDetails = room_details.map((detail) => ({
        room_type: detail.room_type,
        room_type_id: availableRoomTypes.find(
          (t) => t.name === detail.room_type
        )?.id,
        rate_plan: "Standard Rate Plan",
        rate_plan_id: undefined,
        rooms: detail.rooms.length,
        guest_count: detail.rooms.map((room) => ({
          adult: room.adults,
          child: room.children,
        })),
        rate: detail.rooms.reduce(
          (acc, room) => acc + parseFloat(room.rate),
          0
        ),
        room_numbers: detail.rooms.map((room) => room.room_number),
      }));

      setFormData((prevFormData) => ({
        ...prevFormData,
        check_in: booking.check_in,
        check_out: booking.check_out,
        nights: calculateNights(booking.check_in, booking.check_out),
        booking_source: booking.booking_source || "Booking Engine",
        booking_type: booking.booking_type || "",
        pay_at_hotel: booking.payment_status === "pending",
        room_details: roomDetails,
        returning_guest: false,
        guest_info: {
          name: customer.name,
          phone: customer.phone,
          email: customer.email,
        },
      }));
      setPrefilled(true); // Prevent future resets
    }
  }, [location.state, availableRoomTypes, prefilled]);

  const debouncedCheckRoomAvailability = useCallback(
    debounce(() => {
      if (formData.check_in && formData.check_out) {
        checkRoomAvailability();
      }
    }, 500),
    [formData.check_in, formData.check_out]
  );

  useEffect(() => {
    debouncedCheckRoomAvailability();
    return () => debouncedCheckRoomAvailability.cancel();
  }, [formData.check_in, formData.check_out]);

  //  useEffect(() => {
  //     if (formData.check_in && formData.check_out) {
  //       checkRoomAvailability();
  //     }
  //   }, [formData.check_in, formData.check_out]);

  // Check room availability when check-in and check-out dates change
  // useEffect(() => {
  //   if (formData.check_in && formData.check_out) {
  //     checkRoomAvailability();
  //   }
  // }, [formData.check_in, formData.check_out]);

  // Function to check room availabilityz

  // ... existing code ...
  const checkRoomAvailability = async () => {
    if (!formData.check_in || !formData.check_out) {
      return;
    }
    if (new Date(formData.check_in) > new Date(formData.check_out)) {
      toast.error("Check-in date should not be greater than check-out date.");
      return;
    }
    try {
      setLoading(true);
      const response = await userApi.checkRoomAvailability(
        formData.check_in,
        formData.check_out
      );
      if (response.data && response.data.availableRoomTypes) {
        const roomTypes = response.data.availableRoomTypes.map(
          (roomType: any) => ({
            id: roomType.room_type_id,
            name: roomType.room_type_name,
            description: roomType.description,
            available_count:
              roomType.available_rooms ||
              (roomType.rooms ? roomType.rooms.length : 0),
            base_rate: parseFloat(roomType.base_rate) || 0,
            max_adults: roomType.max_adults || 2,
            max_children: roomType.max_children || 2,
          })
        );
        setAvailableRoomTypes(roomTypes);
        const roomNumbersMap: { [roomTypeId: number]: RoomNumberOption[] } = {};
        roomTypes.forEach((roomType: any) => {
          const roomTypeId = roomType.id;
          const apiRoomType = response.data.availableRoomTypes.find(
            (rt: any) => rt.room_type_id === roomTypeId
          );
          if (
            apiRoomType &&
            apiRoomType.rooms &&
            Array.isArray(apiRoomType.rooms)
          ) {
            roomNumbersMap[roomTypeId] = apiRoomType.rooms.map((room: any) => ({
              id: room.id || room.room_id,
              room_number: room.room_number,
            }));
          }
        });
        // Correction: Merge edit mode room numbers into dropdown options
        if (editMode && location.state?.editBooking) {
          const { room_details } = location.state.editBooking;
          room_details.forEach((detail: any) => {
            const typeId = roomTypes.find(
              (t: any) => t.name === detail.room_type
            )?.id;
            if (typeId) {
              detail.rooms.forEach((room: any) => {
                if (
                  roomNumbersMap[typeId] &&
                  !roomNumbersMap[typeId].some(
                    (r) => r.room_number === room.room_number
                  )
                ) {
                  roomNumbersMap[typeId].push({
                    id: room.room_id || room.id, // Use room_id from backend
                    room_number: room.room_number,
                  });
                }
              });
            }
          });
        }
        setAvailableRoomNumbers({ ...roomNumbersMap });
      } else {
        toast.warning("No available rooms found for the selected dates");
      }
    } catch (error: any) {
      console.error("Error checking room availability:", error);
      toast.error(
        error?.response?.data?.message ||
          error.response?.data?.error ||
          error.message ||
          "Failed to check room availability. Please try again."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRoomChange = (
    roomIndex: number,
    field: string,
    value: any,
    subIndex?: number
  ) => {
    const updatedRooms = formData.room_details.map((room) => ({
      ...room,
      room_numbers: [...room.room_numbers],
      guest_count: room.guest_count.map((gc) => ({ ...gc })),
    }));

    if (field === "room_type") {
      const selectedRoomType = availableRoomTypes.find(
        (rt) => rt.name === value
      );
      if (selectedRoomType) {
        updatedRooms[roomIndex] = {
          ...updatedRooms[roomIndex],
          room_type: value,
          room_type_id: selectedRoomType.id,
          rate_plan: "",
          rate_plan_id: undefined,
          rate: selectedRoomType.base_rate || 0,
          rooms: 1,
          room_numbers: [""],
          guest_count: [{ adult: 1, child: 0 }],
        };
      }
    } else if (field === "rooms") {
      const count = Math.max(1, value);
      updatedRooms[roomIndex].rooms = count;
      updatedRooms[roomIndex].room_numbers = Array(count)
        .fill("")
        .map((_, i) => updatedRooms[roomIndex].room_numbers[i] || "");
      // Adjust guest_count array
      updatedRooms[roomIndex].guest_count = Array(count)
        .fill(null)
        .map(
          (_, i) =>
            updatedRooms[roomIndex].guest_count[i] || { adult: 1, child: 0 }
        );
    } else if (field === "room_number" && typeof subIndex === "number") {
      updatedRooms[roomIndex].room_numbers[subIndex] = value;
    } else if (
      (field === "adult" || field === "child") &&
      typeof subIndex === "number"
    ) {
      updatedRooms[roomIndex].guest_count[subIndex] = {
        ...updatedRooms[roomIndex].guest_count[subIndex],
        [field]: value,
      };
    } else {
      updatedRooms[roomIndex] = {
        ...updatedRooms[roomIndex],
        [field]: value,
      };
    }

    setFormData({ ...formData, room_details: updatedRooms });
  };

  const addRoom = () => {
    // Check if we have available room types after some have been selected
    const selectedRoomTypeIds = formData.room_details
      .map((room) => room.room_type_id)
      .filter((id) => id !== undefined);

    // Filter out already selected room types
    const remainingRoomTypes = availableRoomTypes.filter(
      (rt) => !selectedRoomTypeIds.includes(rt.id) && rt.available_count > 0
    );

    if (remainingRoomTypes.length === 0) {
      toast.warning("No more room types available for the selected dates");
      return;
    }

    // Add a new room detail
    setFormData({
      ...formData,
      room_details: [
        ...formData.room_details,
        {
          room_type: "",
          room_type_id: undefined,
          rate_plan: "",
          rate_plan_id: undefined,
          rooms: 1,
          guest_count: [{ adult: 1, child: 0 }],
          rate: 0,
          room_numbers: [],
        },
      ],
    });
  };

  const removeRoom = (index: number) => {
    // Remove this room's selected numbers
    const newSelectedRoomNumbers = { ...selectedRoomNumbers };
    delete newSelectedRoomNumbers[index];

    // Update selected room numbers state
    setSelectedRoomNumbers(newSelectedRoomNumbers);

    // Remove the room from form data
    const updatedRooms = formData.room_details.filter((_, i) => i !== index);
    setFormData({ ...formData, room_details: updatedRooms });
  };

  const handleGuestSearch = async (phone: string) => {
    if (!phone || phone.length < 10) {
      toast.error("Please enter a valid phone number");
      return;
    }

    try {
      setLoading(true);
      const response = await userApi.searchCustomer(phone);

      if (response.data && response.data.customer) {
        // Customer found
        const customer = response.data.customer;
        setFormData({
          ...formData,
          guest_info: {
            id: customer.id,
            name: customer.name,
            phone: customer.phone, // <-- use .phone
            email: customer.email || "",
          },
        });
        toast.success("Customer found!");
      } else {
        // Customer not found
        toast.info("Customer not found. Please enter customer details.");
        setFormData({
          ...formData,
          guest_info: {
            name: "",
            phone: phone, // <-- use .phone
            email: "",
          },
        });
      }
    } catch (error) {
      console.error("Error searching for customer:", error);
      toast.error("Failed to search for customer. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const calculateNights = (checkIn: string, checkOut: string) => {
    const checkInDate = new Date(checkIn);
    const checkOutDate = new Date(checkOut);
    const timeDiff = checkOutDate.getTime() - checkInDate.getTime();
    return timeDiff > 0 ? Math.ceil(timeDiff / (1000 * 3600 * 24)) : 0;
  };

  const validateForm = () => {
    let hasError = false;
    const newBookingFieldErrors: { [field: string]: string } = {};
    const newRoomFieldErrors: {
      [roomIdx: number]: { [field: string]: string };
    } = {};
    const newGuestFieldErrors: { [field: string]: string } = {};

    // Booking Information validation
    if (!formData.booking_source) {
      newBookingFieldErrors.booking_source = "Please select booking source";
      hasError = true;
    }
    if (!formData.booking_type) {
      newBookingFieldErrors.booking_type = "Please select booking type";
      hasError = true;
    }
    if (!formData.check_in) {
      newBookingFieldErrors.check_in = "Please select check-in date";
      hasError = true;
    }
    if (!formData.check_out) {
      newBookingFieldErrors.check_out = "Please select check-out date";
      hasError = true;
    }

    if (formData.check_in && formData.check_out) {
      const checkInDate = new Date(formData.check_in);
      const checkOutDate = new Date(formData.check_out);
      if (checkInDate > checkOutDate) {
        newBookingFieldErrors.check_out =
          "Check-out date must be after check-in date";
        hasError = true;
      }
    }

    // Room Details validation
    for (let i = 0; i < formData.room_details.length; i++) {
      const room = formData.room_details[i];
      const errors: { [field: string]: string } = {};

      if (!room.room_type) {
        errors.room_type = "Please select room type";
        hasError = true;
      }
      for (let j = 0; j < room.room_numbers.length; j++) {
        if (!room.room_numbers[j] || room.room_numbers[j].trim() === "") {
          errors[`room_number_${j}`] = `Please select room number for Room ${
            j + 1
          }`;
          hasError = true;
        }
      }
      for (let j = 0; j < room.guest_count.length; j++) {
        if (!room.guest_count[j] || room.guest_count[j].adult < 1) {
          errors[`adult_${j}`] = "At least 1 adult required";
          hasError = true;
        }
      }
      if (Object.keys(errors).length > 0) {
        newRoomFieldErrors[i] = errors;
      }
    }

    // Guest Info validation
    if (!formData.guest_info.name) {
      newGuestFieldErrors.name = "Please enter guest name";
      hasError = true;
    }
    if (!formData.guest_info.phone) {
      newGuestFieldErrors.phone = "Please enter guest phone number";
      hasError = true;
    } else if (!/^\d{10}$/.test(formData.guest_info.phone)) {
      newGuestFieldErrors.phone = "Phone number must be exactly 10 digits";
      hasError = true;
    }
    if (
      formData.guest_info.email &&
      !/^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/.test(
        formData.guest_info.email
      )
    ) {
      newGuestFieldErrors.email = "Please enter a valid email address";
      hasError = true;
    }

    setBookingFieldErrors(newBookingFieldErrors);
    setRoomFieldErrors(newRoomFieldErrors);
    setGuestFieldErrors(newGuestFieldErrors);

    // Show first error as toast for user attention
    if (hasError) {
      const firstBookingError = Object.values(newBookingFieldErrors)[0];
      const firstRoom = Object.values(newRoomFieldErrors)[0];
      const firstRoomError = firstRoom && Object.values(firstRoom)[0];
      const firstGuestError = Object.values(newGuestFieldErrors)[0];
      if (firstBookingError) toast.error(firstBookingError);
      else if (firstRoomError) toast.error(firstRoomError);
      else if (firstGuestError) toast.error(firstGuestError);
      return false;
    }

    return true;
  };

 const submitBooking = async () => {
    if (!validateForm()) return;
    try {
      setLoading(true);

      // Flatten all room details into a single array as required by backend
      const flatRoomBookings = formData.room_details.flatMap((room) =>
        room.room_numbers.map((room_number, idx) => ({
          room_number,
          room_type: room.room_type, // RoomType name, not id
          rate: room.rate,
          adults: room.guest_count[idx]?.adult ?? 1,
          children: room.guest_count[idx]?.child ?? 0,
        }))
      );

      // Calculate total_amount as sum of all room rates
      const total_amount = Number(totalPrice);

      const bookingData = {
        customer_info: {
          name: formData.guest_info.name,
          phone: formData.guest_info.phone,
          email: formData.guest_info.email,
        },
        booking_info: {
          check_in: formData.check_in,
          check_out: formData.check_out,
          booking_source: formData.booking_source,
          booking_type: formData.booking_type,
          total_amount,
          payment_status: formData.pay_at_hotel ? "pending" : "paid",
          booking_status: "confirmed",
        },
      };

      let response;
      if (editMode && editBookingId) {
        // For update, send view_room_bookings (flat array)
        response = await userApi.updateBooking(editBookingId, {
          ...bookingData,
          view_room_bookings: flatRoomBookings,
        });
      } else {
        // For create, send room_bookings (flat array)
        response = await userApi.createBooking({
          ...bookingData,
          room_bookings: flatRoomBookings,
        });
      }

      if (response.data && response.data.message) {
        toast.success(
          editMode
            ? "Booking updated successfully!"
            : "Booking created successfully!"
        );
        navigate("/bookings");
      } else {
        throw new Error(
          editMode ? "Failed to update booking" : "Failed to create booking"
        );
      }
    } catch (error: any) {
      console.error("Error saving booking:", error);
      const backendMsg =
        error?.response?.data?.message ||
        error?.response?.data?.error ||
        error?.message;
      toast.error(
        backendMsg ||
          (editMode
            ? "Failed to update booking. Please try again."
            : "Failed to create booking. Please try again.")
      );
    } finally {
      setLoading(false);
    }
  };

  // Helper to safely parse date string (yyyy-MM-dd) or fallback to today
  const parseDate = (dateStr: string | undefined) => {
    if (!dateStr) return today;
    // Accept only yyyy-MM-dd, ignore dd/MM/yyyy or invalid
    const parts = dateStr.split("-");
    if (parts.length === 3) {
      const d = new Date(dateStr);
      if (!isNaN(d.getTime())) return d;
    }
    // Try to convert dd/MM/yyyy to yyyy-MM-dd
    if (dateStr.includes("/")) {
      const [dd, mm, yyyy] = dateStr.split("/");
      if (dd && mm && yyyy) {
        const d = new Date(`${yyyy}-${mm}-${dd}`);
        if (!isNaN(d.getTime())) return d;
      }
    }
    return today;
  };

  // Helper to get all room types for dropdown: always show all types, and always show prefilled/selected type
  const getRoomTypeOptions = (roomIndex: number) => {
    const selectedRoomTypeName = formData.room_details[roomIndex]?.room_type;
    let options = [...availableRoomTypes];
    if (
      editMode &&
      selectedRoomTypeName &&
      !options.some((rt) => rt.name === selectedRoomTypeName)
    ) {
      options.push({
        id: 100000 + roomIndex,
        name: selectedRoomTypeName,
        description: "",
        available_count: 0,
      });
    }
    return options;
  };

  // Helper to get all room numbers for dropdown: available + booked for this booking
  const getRoomNumberOptions = (
    room_type_id: number,
    bookedRoomNumbers: string[],
    roomIndex: number
  ): RoomNumberOption[] => {
    const available = availableRoomNumbers[room_type_id] || [];
    let extra: RoomNumberOption[] = [];
    if (editMode && bookedRoomNumbers && bookedRoomNumbers.length > 0) {
      extra = bookedRoomNumbers
        .filter((num) => num && !available.some((r) => r.room_number === num))
        .map((num, idx) => ({
          id: 100000 + roomIndex * 100 + idx,
          room_number: num,
        }));
    }
    return [...available, ...extra];
  };

  return (
    <div className="w-full p-3">
      <h1 className="text-2xl font-semibold text-gray-900 mb-3 mt-[-2.5]">
        {editMode ? "Edit Booking" : "Add Booking"}
      </h1>
      <div className="grid grid-cols-3 gap-6">
        {/* Booking Information */}
        <div className="col-span-2 p-4 bg-white rounded-md">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Booking Information
          </h3>
          {/* Booking Information */}
          <div className="grid grid-cols-6 gap-4 items-center">
            {/* Booking Source */}
            <div className="col-span-3">
              <Label>Booking Source</Label>
              <select
                value={formData.booking_source}
                onChange={(e) => {
                  setFormData({ ...formData, booking_source: e.target.value });
                  setBookingFieldErrors((prev) => {
                    const copy = { ...prev };
                    if (copy.booking_source) delete copy.booking_source;
                    return copy;
                  });
                }}
                className={`w-full p-2 border rounded-md ${
                  bookingFieldErrors.booking_source ? "border-red-500" : ""
                }`}
              >
                <option value="">Select Source</option>
                <option value="Booking Engine">Booking Engine</option>
              </select>
              {bookingFieldErrors.booking_source && (
                <div className="text-red-500 text-xs mt-1">
                  {bookingFieldErrors.booking_source}
                </div>
              )}
            </div>

            {/* Booking Type */}
            <div className="col-span-3">
              <Label>Booking Type</Label>
              <select
                value={formData.booking_type}
                onChange={(e) => {
                  setFormData({ ...formData, booking_type: e.target.value });
                  setBookingFieldErrors((prev) => {
                    const copy = { ...prev };
                    if (copy.booking_type) delete copy.booking_type;
                    return copy;
                  });
                }}
                className={`w-full p-2 border rounded-md ${
                  bookingFieldErrors.booking_type ? "border-red-500" : ""
                }`}
              >
                <option value="">Select Booking Type</option>
                <option value="Confirm Booking">Confirm Booking</option>
                <option value="Tentative">Tentative</option>
                <option value="Hold">Hold</option>
                <option value="Group Booking">Group Booking</option>
              </select>
              {bookingFieldErrors.booking_type && (
                <div className="text-red-500 text-xs mt-1">
                  {bookingFieldErrors.booking_type}
                </div>
              )}
            </div>

            {/* Date Selection Row with Pay at Hotel */}
            <div className="col-span-6">
              <div className="flex items-center w-full">
                {/* Check-In */}
                <div
                  className="border rounded-md overflow-hidden"
                  style={{ width: "180px" }}
                >
                  <div className="px-2 py-1">
                    <div className="text-sm font-medium text-gray-700">
                      Check In
                    </div>
                    <div className="flex items-center">
                      <div className="flex-1">
                        <DatePicker
                          selected={parseDate(formData.check_in)}
                          onChange={(date: Date | null) => {
                            if (date) {
                              const checkIn = date.toISOString().split("T")[0];
                              const nights = calculateNights(
                                checkIn,
                                formData.check_out
                              );
                              setFormData({
                                ...formData,
                                check_in: checkIn,
                                nights,
                              });
                              setBookingFieldErrors((prev) => {
                                const copy = { ...prev };
                                if (copy.check_in) delete copy.check_in;
                                return copy;
                              });
                            }
                          }}
                          dateFormat="yyyy-MM-dd"
                          minDate={today}
                          className={`w-full border rounded-md ${
                            bookingFieldErrors.check_in ? "border-red-500" : ""
                          }`}
                          placeholderText="YYYY-MM-DD"
                        />
                      </div>
                      <CalenderIcon className="size-5 text-gray-500 mr-2" />
                    </div>
                  </div>
                </div>

                {/* Nights Counter */}
                <div className="bg-blue-500 text-white font-semibold px-6 py-2 flex flex-col items-center justify-center">
                  <div className="text-xl">{formData.nights}</div>
                  <div className="text-xs">Nights</div>
                </div>

                {/* Check-Out */}
                <div
                  className="border rounded-md overflow-hidden"
                  style={{ width: "180px" }}
                >
                  <div className="px-2 py-1">
                    <div className="text-sm font-medium text-gray-700">
                      Check Out
                    </div>
                    <div className="flex items-center">
                      <div className="flex-1">
                        <DatePicker
                          selected={parseDate(formData.check_out)}
                          onChange={(date: Date | null) => {
                            if (date) {
                              const checkOut = date.toISOString().split("T")[0];
                              const nights = calculateNights(
                                formData.check_in,
                                checkOut
                              );
                              setFormData({
                                ...formData,
                                check_out: checkOut,
                                nights,
                              });
                              setBookingFieldErrors((prev) => {
                                const copy = { ...prev };
                                if (copy.check_out) delete copy.check_out;
                                return copy;
                              });
                            }
                          }}
                          dateFormat="yyyy-MM-dd"
                          minDate={today}
                          className={`w-full border rounded-md ${
                            bookingFieldErrors.check_out ? "border-red-500" : ""
                          }`}
                          placeholderText="YYYY-MM-DD"
                        />
                      </div>
                      <CalenderIcon className="size-5 text-gray-500 mr-2" />
                    </div>
                  </div>
                </div>

                {/* Pay at Hotel Checkbox */}
                <div className="ml-4">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.pay_at_hotel}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          pay_at_hotel: e.target.checked,
                        })
                      }
                      className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    />
                    <span className="text-gray-700">Pay at Hotel</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Billing Summary */}
        <div className="col-span-1 p-4 bg-white rounded-md">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 uppercase">
            Billing Summary
          </h3>

          {/* From and To with Bed Icon */}
          <div className="flex items-center mb-6 pb-4 border-b border-gray-100">
            <div className="flex-1">
              <div className="text-sm text-gray-600">From</div>
              <div className="font-medium">
                {formData.check_in || "YYYY-MM-DD"}
              </div>
            </div>
            <div className="mx-2 flex items-center justify-center w-10 h-10 bg-gray-100 rounded-full text-gray-400">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect
                  x="3"
                  y="10"
                  width="18"
                  height="7"
                  rx="1"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path
                  d="M5 10V7C5 6.44772 5.44772 6 6 6H18C18.5523 6 19 6.44772 19 7V10"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path
                  d="M3 17H21V19C21 19.5523 20.5523 20 20 20H4C3.44772 20 3 19.5523 3 19V17Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path d="M7 6V10" stroke="currentColor" strokeWidth="1.5" />
                <path d="M17 6V10" stroke="currentColor" strokeWidth="1.5" />
              </svg>
            </div>
            <div className="flex-1 text-right">
              <div className="text-sm text-gray-600">To</div>
              <div className="font-medium">
                {formData.check_out || "YYYY-MM-DD"}
              </div>
            </div>
          </div>

          <div className="mb-6">
            <div className="flex justify-between mb-3">
              <span className="text-gray-600">Room Charges</span>
              <span className="font-medium">Rs.{totalPrice}</span>
            </div>
            <div className="flex justify-between font-semibold">
              <span>Total Price</span>
              <span className="text-blue-600">Rs.{totalPrice}</span>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <button
              className="px-6 py-2 border border-green-500 text-green-500 rounded-md hover:bg-green-50 font-medium uppercase text-sm"
              // onClick={submitBooking}
              disabled={loading}
            >
              {loading
                ? "Processing..."
                : formData.booking_type || "Confirm Booking"}
            </button>
            <div className="text-gray-400">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 12C14.2091 12 16 10.2091 16 8C16 5.79086 14.2091 4 12 4C9.79086 4 8 5.79086 8 8C8 10.2091 9.79086 12 12 12Z"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path
                  d="M20 19H19V17C19 14.7909 17.2091 13 15 13H9C6.79086 13 5 14.7909 5 17V19H4"
                  stroke="currentColor"
                  strokeWidth="1.5"
                />
                <path d="M16 19H8" stroke="currentColor" strokeWidth="1.5" />
                <path d="M12 19V13" stroke="currentColor" strokeWidth="1.5" />
              </svg>
            </div>
          </div>
        </div>

        {/* Room Details */}
        <div className="col-span-3 p-4 bg-white rounded-md">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Room Details
          </h3>
          {formData.room_details.map((room, roomIndex) => (
            <div key={roomIndex} className="border p-4 mb-4 rounded-md">
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div>
                  <Label>Room Type</Label>
                  <select
                    value={room.room_type}
                    onChange={(e) => {
                      handleRoomChange(roomIndex, "room_type", e.target.value);
                      setRoomFieldErrors((prev) => {
                        const copy = { ...prev };
                        if (copy[roomIndex]?.room_type) {
                          delete copy[roomIndex].room_type;
                          if (Object.keys(copy[roomIndex]).length === 0)
                            delete copy[roomIndex];
                        }
                        return copy;
                      });
                    }}
                    className={`w-full p-2 border rounded-md ${
                      roomFieldErrors[roomIndex]?.room_type
                        ? "border-red-500"
                        : ""
                    }`}
                    disabled={
                      loading || !formData.check_in || !formData.check_out
                    }
                  >
                    <option value="">Select Room Type</option>
                    {getRoomTypeOptions(roomIndex).map((rt) => {
                      // Calculate available rooms for this type (do not exclude current selection)
                      const totalReserved = formData.room_details
                        .filter(
                          (r, idx) =>
                            r.room_type_id === rt.id && idx !== roomIndex
                        )
                        .reduce((sum, r) => sum + r.rooms, 0);
                      const remainingRooms = rt.available_count - totalReserved;
                      return (
                        <option key={rt.id} value={rt.name}>
                          {rt.name} ({remainingRooms} available)
                        </option>
                      );
                    })}
                  </select>
                  {roomFieldErrors[roomIndex]?.room_type && (
                    <div className="text-red-500 text-xs mt-1">
                      {roomFieldErrors[roomIndex].room_type}
                    </div>
                  )}
                </div>
                {/* <div>
                  <Label>Rate Plan</Label>
                  <select
                    value={room.rate_plan}
                    onChange={(e) =>
                      handleRoomChange(roomIndex, "rate_plan", e.target.value)
                    }
                    className="w-full p-2 border rounded-md"
                    disabled={loading || !room.room_type_id}
                  >
                    <option value="">Select Rate Plan</option>
                    {room.room_type_id &&
                    availableRatePlans[room.room_type_id] ? (
                      availableRatePlans[room.room_type_id].map((rp) => (
                        <option key={rp.id} value={rp.name}>
                          {rp.name} (Rs. {rp.price})
                        </option>
                        
                       
                      ))
                   
                    ) : (
                      <option value="" disabled>
                        {room.room_type
                          ? "No rate plans available"
                          : "Select room type first"}
                      </option>
                    )}
                  </select>
                </div> */}
                <div>
                  <Label>Rate Plan</Label>
                  <select
                    value={room.rate_plan}
                    onChange={(e) =>
                      handleRoomChange(roomIndex, "rate_plan", e.target.value)
                    }
                    className="w-full p-2 border rounded-md"
                    disabled={loading || !room.room_type_id}
                  >
                    <option value="">Select Rate Plan</option>
                    <option value="Standard Rate Plan">
                      Standard Rate Plan
                    </option>
                    {room.room_type_id &&
                    availableRatePlans[room.room_type_id] ? (
                      availableRatePlans[room.room_type_id].map((rp) => (
                        <option key={rp.id} value={rp.name}>
                          {rp.name} (Rs. {rp.price})
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>
                        {room.room_type
                          ? "No rate plans available"
                          : "Select room type first"}
                      </option>
                    )}
                  </select>
                </div>
                <div>
                  <Label>Rooms</Label>
                  <Input
                    type="number"
                    value={room.rooms.toString()}
                    onChange={(e) => {
                      // Find the available count for this room type
                      const selectedRoomType = availableRoomTypes.find(
                        (rt) => rt.id === room.room_type_id
                      );
                      const maxRooms = selectedRoomType
                        ? selectedRoomType.available_count
                        : 1;
                      // Clamp value to maxRooms
                      let value = parseInt(e.target.value) || 1;
                      if (value > maxRooms) value = maxRooms;
                      if (value < 1) value = 1;
                      handleRoomChange(roomIndex, "rooms", value);
                    }}
                    min="1"
                    max={(() => {
                      const selectedRoomType = availableRoomTypes.find(
                        (rt) => rt.id === room.room_type_id
                      );
                      return selectedRoomType
                        ? selectedRoomType.available_count
                        : 1;
                    })()}
                    className="w-full p-2 border rounded-md"
                  />
                </div>
              </div>
              <div className="flex justify-between items-center mb-4">
                <span className="text-gray-700 font-semibold">
                  {room.rate_plan || "Rate Plan"}
                </span>
                <button
                  type="button"
                  onClick={() => removeRoom(roomIndex)}
                  className="text-red-500 hover:text-red-700"
                >
                  Remove Room Type
                </button>
              </div>
              <div className="border-t pt-4">
                {[...Array(room.rooms)].map((_, subIndex) => {
                  // Get all selected room numbers except for this slot
                  const allSelected = formData.room_details.flatMap((r, rIdx) =>
                    r.room_numbers.filter(
                      (num, nIdx) => !(rIdx === roomIndex && nIdx === subIndex)
                    )
                  );
                  // Get booked room numbers for this room detail (from prefill)
                  const bookedRoomNumbers = room.room_numbers || [];
                  // Show available + booked for this booking
                  const availableNumbers = room.room_type_id
                    ? getRoomNumberOptions(
                        room.room_type_id,
                        bookedRoomNumbers,
                        roomIndex
                      ).filter(
                        (rn) =>
                          !allSelected.includes(rn.room_number) ||
                          rn.room_number === room.room_numbers[subIndex]
                      )
                    : [];

                  return (
                    <div key={subIndex} className="grid grid-cols-5 gap-3 mb-4">
                      <span className="text-gray-700 font-semibold flex items-center">
                        Room {subIndex + 1}
                      </span>
                      {/* Room Number Dropdown */}
                      <div>
                        <Label>Room Number</Label>
                        <select
                          className={`w-full p-2 border rounded-md ${
                            roomFieldErrors[roomIndex]?.[
                              `room_number_${subIndex}`
                            ]
                              ? "border-red-500"
                              : ""
                          }`}
                          disabled={loading || !room.room_type_id}
                          onChange={(e) => {
                            handleRoomChange(
                              roomIndex,
                              "room_number",
                              e.target.value,
                              subIndex
                            );
                            setRoomFieldErrors((prev) => {
                              const copy = { ...prev };
                              if (
                                copy[roomIndex]?.[`room_number_${subIndex}`]
                              ) {
                                delete copy[roomIndex][
                                  `room_number_${subIndex}`
                                ];
                                if (Object.keys(copy[roomIndex]).length === 0)
                                  delete copy[roomIndex];
                              }
                              return copy;
                            });
                          }}
                          value={room.room_numbers[subIndex] || ""}
                        >
                          <option value="">Select Room Number</option>
                          {availableNumbers.map((rn) => (
                            <option key={rn.id} value={rn.room_number}>
                              {rn.room_number}
                            </option>
                          ))}
                        </select>
                        {roomFieldErrors[roomIndex]?.[
                          `room_number_${subIndex}`
                        ] && (
                          <div className="text-red-500 text-xs mt-1">
                            {
                              roomFieldErrors[roomIndex][
                                `room_number_${subIndex}`
                              ]
                            }
                          </div>
                        )}
                      </div>
                      {/* Adults */}
                      <div>
                        <Label>Adults</Label>
                        <Input
                          type="number"
                          value={
                            room.guest_count[subIndex]?.adult?.toString() || "1"
                          }
                          onChange={(e) => {
                            handleRoomChange(
                              roomIndex,
                              "adult",
                              parseInt(e.target.value) || 0,
                              subIndex
                            );
                            setRoomFieldErrors((prev) => {
                              const copy = { ...prev };
                              if (copy[roomIndex]?.[`adult_${subIndex}`]) {
                                delete copy[roomIndex][`adult_${subIndex}`];
                                if (Object.keys(copy[roomIndex]).length === 0)
                                  delete copy[roomIndex];
                              }
                              return copy;
                            });
                          }}
                          min="1"
                          className={`w-full p-2 border rounded-md ${
                            roomFieldErrors[roomIndex]?.[`adult_${subIndex}`]
                              ? "border-red-500"
                              : ""
                          }`}
                        />
                        {roomFieldErrors[roomIndex]?.[`adult_${subIndex}`] && (
                          <div className="text-red-500 text-xs mt-1">
                            {roomFieldErrors[roomIndex][`adult_${subIndex}`]}
                          </div>
                        )}
                      </div>
                      {/* Children */}
                      <div>
                        <Label>Children</Label>
                        <Input
                          type="number"
                          value={
                            room.guest_count[subIndex]?.child?.toString() || "0"
                          }
                          onChange={(e) =>
                            handleRoomChange(
                              roomIndex,
                              "child",
                              parseInt(e.target.value) || 0,
                              subIndex
                            )
                          }
                          min="0"
                          className="w-full p-2 border rounded-md"
                        />
                      </div>
                      {/* Rate (read-only) */}
                      <div>
                        <Label>Rate</Label>
                        <Input
                          type="number"
                          value={room.rate}
                          readOnly
                          className="w-full p-2 border rounded-md bg-gray-100"
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
          <button
            type="button"
            onClick={addRoom}
            className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
          >
            + Add Another
          </button>
        </div>

        {/* Guest Information */}
        <div className="col-span-3 p-4 bg-white rounded-md">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            Guest Information
          </h3>
          {/* Returning Guest */}
          <div className="flex items-center gap-4 mb-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="guest_type"
                checked={formData.returning_guest}
                onChange={() =>
                  setFormData({ ...formData, returning_guest: true })
                }
              />
              Returning Guest
            </label>
            <Input
              type="text"
              placeholder="Enter phone number"
              value={returningphone}
              onChange={(e) => setReturningphone(e.target.value)}
              className={`w-1/2 ${
                !formData.returning_guest
                  ? "bg-gray-200 cursor-not-allowed"
                  : ""
              }`}
              disabled={!formData.returning_guest}
            />
            <button
              className={`px-4 py-2 rounded-md ${
                formData.returning_guest
                  ? "bg-blue-500 text-white hover:bg-blue-600"
                  : "bg-gray-300 text-gray-500 cursor-not-allowed"
              }`}
              disabled={!formData.returning_guest}
              onClick={() => handleGuestSearch(returningphone)}
            >
              Search
            </button>
          </div>

          {/* New Guest */}
          <div>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="guest_type"
                checked={!formData.returning_guest}
                onChange={() => {
                  setFormData({
                    ...formData,
                    returning_guest: false,
                    guest_info: { name: "", phone: "", email: "" },
                  });
                  setReturningphone(""); // Also clear the returning guest phone input
                }}
              />
              New Guest
            </label>
          </div>

          {/* Guest Details */}
          <div className="grid grid-cols-3 gap-4 mt-4">
            <div>
              <Label>Guest Name</Label>
              <Input
                type="text"
                value={formData.guest_info.name}
                onChange={(e) => {
                  setFormData({
                    ...formData,
                    guest_info: {
                      ...formData.guest_info,
                      name: e.target.value,
                    },
                  });
                  setGuestFieldErrors((prev) => {
                    const copy = { ...prev };
                    if (copy.name) delete copy.name;
                    return copy;
                  });
                }}
                readOnly={formData.returning_guest}
                className={`w-full p-2 border rounded-md ${
                  guestFieldErrors.name ? "border-red-500" : ""
                }`}
              />
              {guestFieldErrors.name && (
                <div className="text-red-500 text-xs mt-1">
                  {guestFieldErrors.name}
                </div>
              )}
            </div>
            <div>
              <Label>Phone Number</Label>
              <Input
                type="text"
                value={formData.guest_info.phone}
                onChange={(e) => {
                  // Only allow digits and max 10 chars
                  const val = e.target.value.replace(/\D/g, "").slice(0, 10);
                  setFormData({
                    ...formData,
                    guest_info: {
                      ...formData.guest_info,
                      phone: val,
                    },
                  });
                  setGuestFieldErrors((prev) => {
                    const copy = { ...prev };
                    if (copy.phone) delete copy.phone;
                    return copy;
                  });
                }}
                readOnly={formData.returning_guest}
                maxLength={10}
                pattern="\d{10}"
                className={`w-full p-2 border rounded-md ${
                  guestFieldErrors.phone ? "border-red-500" : ""
                }`}
              />
              {guestFieldErrors.phone && (
                <div className="text-red-500 text-xs mt-1">
                  {guestFieldErrors.phone}
                </div>
              )}
            </div>
            <div>
              <Label>Email</Label>
              <Input
                type="email"
                value={formData.guest_info.email}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    guest_info: {
                      ...formData.guest_info,
                      email: e.target.value,
                    },
                  })
                }
                readOnly={formData.returning_guest}
                className={`w-full p-2 border rounded-md ${
                  guestFieldErrors.email ? "border-red-500" : ""
                }`}
              />
              {guestFieldErrors.email && (
                <div className="text-red-500 text-xs mt-1">
                  {guestFieldErrors.email}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 mt-6">
            <button
              type="button"
              onClick={() => window.history.back()}
              className="px-4 py-1.5 bg-gray-700 text-white text-sm uppercase font-medium rounded"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={submitBooking}
              disabled={loading}
              className="px-4 py-1.5 bg-blue-500 text-white text-sm uppercase font-medium rounded"
            >
              {loading ? "Processing..." : "Save"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddBooking;
