import { useState, useEffect, useCallback } from "react";

interface SelectedDate {
  room_type_id: string;
  date: Date;
}

export const useRoomRateSelection = () => {
  

  const [roomTypeId, setRoomTypeId] = useState("");
  const [selectedDates, setSelectedDates] = useState<SelectedDate[]>([]);
  const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);
  const [isDragging, setIsDragging] = useState<boolean>(false); // Track dragging state
  const [selectedRoomTypeForUpdate, setSelectedRoomTypeForUpdate] = useState<string>("");

  // Check if a date is selected
  const isDateSelected = useCallback((room_type_id: string, date: Date) => {
    return selectedDates.some(
      (selectedDate) =>
        selectedDate.room_type_id === room_type_id &&
        selectedDate.date.toDateString() === date.toDateString()
    );
  }, [selectedDates]);

  // Toggle date selection
  const toggleDateSelection = (roomTypeId: string, date: Date) => {
    setSelectedDates((prevDates) => {
      // Clear dates if a new room type is selected
      if (prevDates.some((d) => d.room_type_id !== roomTypeId)) {
        return [{ room_type_id: roomTypeId, date }];
      }
  
      // Toggle the current date selection
      const isAlreadySelected = prevDates.some(
        (d) => d.room_type_id === roomTypeId && d.date.toISOString() === date.toISOString()
      );
  
      if (isAlreadySelected) {
        return prevDates.filter(
          (d) => !(d.room_type_id === roomTypeId && d.date.toISOString() === date.toISOString())
        );
      }
  
      return [...prevDates, { room_type_id: roomTypeId, date }];
    });
  
    // Update the selected room type for the popup
    setSelectedRoomTypeForUpdate(roomTypeId);
  };

  // Start dragging
  const startDragging = useCallback(() => {
    setIsDragging(true);
  }, []);

  // Stop dragging and show pop-up if multiple dates are selected
  const stopDragging = useCallback(() => {
    setIsDragging(false);
    if (selectedDates.length > 1) {
      setSelectedRoomTypeForUpdate(selectedDates[0].room_type_id); // Set the room type for the pop-up
      setIsPopupOpen(true); // Show the pop-up
    }
  }, [selectedDates]);

  // Clear selected dates
  const clearSelectedDates = useCallback(() => {
    setSelectedDates([]);
  }, []);

  // Close pop-up
  const closePopup = useCallback(() => {
    setIsPopupOpen(false);
  }, []);


  const selectRoomType = (newRoomTypeId: string) => {
    setRoomTypeId(newRoomTypeId);
    setSelectedDates((prevDates) =>
      prevDates.filter((dateObj) => dateObj.room_type_id === newRoomTypeId)
    );
  };

  return {
    selectedDates,
    setSelectedDates,
    toggleDateSelection,
    isDateSelected,
    isPopupOpen,
    closePopup,
    clearSelectedDates,
    selectedRoomTypeForUpdate,
    setSelectedRoomTypeForUpdate,
    startDragging,
    stopDragging,
    isDragging,
    roomTypeId, 
    selectRoomType
  };
};