import { useEffect, useState } from "react";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { Eye, EyeOff } from "lucide-react";
import { useAuth } from "../../context/AuthContext";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";
import { jwtDecode } from "jwt-decode";
interface User {
  id?: number;
  first_name: string;
  last_name: string;
  phone: string;
  email: string;
  password: string;
  role_name: string;

  hotel_name?: string;
  Role?: {
    role_name: string;
  };
  Hotel?: {
    Hotel_name: string;
  };
}

export const UserManagement = () => {
  const { TokenFROMLSGet } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [formData, setFormData] = useState<User>({
    first_name: "",
    last_name: "",
    phone: "",
    email: "",
    password: "Default@123",
    role_name: "",
    hotel_name: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [sortField, setSortField] = useState<string>("");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [showPassword, setShowPassword] = useState<boolean>(false);

  // Current user information
  const [currentUserRole, setCurrentUserRole] = useState<string>("");

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [roles, setRoles] = useState<{ id: number; role_name: string }[]>([]);
  const [hotels, setHotels] = useState<{ id: number; Hotel_name: string }[]>(
    []
  );
  const [isLoadingHotels, setIsLoadingHotels] = useState<boolean>(false);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Add these state variables
  const [currentUserHotelName, setCurrentUserHotelName] = useState<string>("");
 

  const fetchUsers = async () => {
    try {
      const response = await userApi.getUserList(
        currentPage,
        itemsPerPage,
        "first_name",
        search,
        true,
        true
      );
      setUsers(response.data.users || []);
      setTotalItems(response.data.totalCount || 0);
    } catch (error) {
      toast.error("Error fetching users");
      console.error("Error fetching users:", error);
    }
  };
  const fetchRoles = async () => {
    try {
      // Fetch all available roles with a single API call
      const response = await userApi.getAllRoles();
      console.log("Roles response:", response);

      if (response.data) {
        setRoles(response.data);
      }
    } catch (error) {
      console.error("Error fetching roles:", error);
    }
  };

  const fetchHotels = async () => {
    try {
      setIsLoadingHotels(true);
      // Fetch all hotel names
      const response = await userApi.getAllHotelNames();
      console.log("Hotels response:", response);

      if (response.data && response.data.hotels) {
        setHotels(response.data.hotels);
      }
    } catch (error) {
      console.error("Error fetching hotels:", error);
      toast.error("Failed to load hotels");
    } finally {
      setIsLoadingHotels(false);
    }
  };

  // Fetch current user information
  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = TokenFROMLSGet();
        if (token) {
          // Decode the token to get user information
          const decoded: any = jwtDecode(token);
          
          // Get role information
          if (decoded.role_id) {
            const roleResponse = await userApi.getRoleName(decoded.role_id.toString());
            if (roleResponse.data && roleResponse.data.role_name) {
              setCurrentUserRole(roleResponse.data.role_name);
              console.log("Current user role:", roleResponse.data.role_name);
            }
          }
          
          // Get hotel information
          if (decoded.hotel_id) {
            // setCurrentUserHotelId(decoded.hotel_id);
            // Fetch hotel name for the current user
            const hotelResponse = await userApi.getHotelById(decoded.hotel_id);
            console.log("Hotel response:", hotelResponse);
            
            // Check the correct property in the response
            if (hotelResponse.data && hotelResponse.data.hotel_name) {
              setCurrentUserHotelName(hotelResponse.data.hotel_name);
              console.log("Setting hotel name:", hotelResponse.data.hotel_name);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };

    fetchCurrentUser();
  }, []);

  useEffect(() => {
    fetchUsers();
    fetchRoles();
    fetchHotels(); // Fetch hotel names for dropdown
  }, [currentPage, itemsPerPage, search, sortField, sortDirection]);

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // No need for client-side filtering as backend now handles role-based filtering
  // Just apply search filter if needed
  const filteredUsers = users.filter((user) => {
    // Apply search filter
    if (
      search &&
      !user.first_name.toLowerCase().startsWith(search.toLowerCase())
    ) {
      return false;
    }
    return true;
  });

  // Sort the filtered users based on sortField and sortDirection
  const sortedUsers = [...filteredUsers].sort((a, b) => {
    if (!sortField) return 0;

    if (sortField === "first_name") {
      // First sort by first_name
      const firstNameA = (a.first_name || "").toLowerCase();
      const firstNameB = (b.first_name || "").toLowerCase();

      // Use localeCompare for proper string comparison
      const firstNameCompare = firstNameA.localeCompare(firstNameB);

      // If first names are the same, sort by last name
      if (firstNameCompare === 0) {
        const lastNameA = (a.last_name || "").toLowerCase();
        const lastNameB = (b.last_name || "").toLowerCase();
        return sortDirection === "asc"
          ? lastNameA.localeCompare(lastNameB)
          : lastNameB.localeCompare(lastNameA);
      }

      // Otherwise return the first name comparison
      return sortDirection === "asc" ? firstNameCompare : -firstNameCompare;
    }

    return 0;
  });

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Open modal for add or edit operation
  const openModal = (type: "add" | "edit", user: User | null = null) => {
    setModalType(type);
    setSelectedUser(user);
    setErrors({});

    if (user) {
      // For editing an existing user
      setFormData({
        first_name: user.first_name || "",
        last_name: user.last_name || "",
        phone: user.phone || "",
        email: user.email || "",
        password: user.password || "Default@123",
        role_name: user.Role?.role_name || user.role_name || "",
        hotel_name: user.Hotel?.Hotel_name || user.hotel_name || "",
      });
    } else {
      // For adding a new user
      const isHotelMasterOrManager = 
        currentUserRole.toLowerCase() === "hotel master" || 
        currentUserRole.toLowerCase() === "manager";
      
      console.log("Is Hotel Master or Manager:", isHotelMasterOrManager);
      console.log("Current hotel name:", currentUserHotelName);
      
      setFormData({
        first_name: "",
        last_name: "",
        phone: "",
        email: "",
        password: "Default@123",
        // If Hotel Master or Manager, prefill with Manager role
        role_name: isHotelMasterOrManager ? "Manager" : "",
        // If Hotel Master or Manager, prefill with their hotel name
        hotel_name: isHotelMasterOrManager ? currentUserHotelName : "",
      });

      // Clear any previous errors
      setErrors({});
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedUser(null);
    setModalType("");
  };

  // Validate form data
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.first_name.trim()) {
      newErrors.first_name = "First name is required";
    }

    if (!formData.last_name.trim()) {
      newErrors.last_name = "Last name is required";
    }

    if (!formData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^\d{10}$/.test(formData.phone.replace(/\D/g, ""))) {
      newErrors.phone = "Phone number should be 10 digits";
    } else if (!/^[6-9]/.test(formData.phone.replace(/\D/g, ""))) {
      newErrors.phone = "Phone number should start with a digit between 6-9";
    }

    if (!formData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Invalid email format";
    }

    if (!formData.password.trim()) {
      newErrors.password = "Password is required";
    } else if (formData.password.length < 6) {
      newErrors.password = "Password must be at least 6 characters long";
    }

    if (!formData.role_name.trim()) {
      newErrors.role_name = "Role is required";
    }

    // Validate hotel_name for all roles
    if (!formData.hotel_name?.trim()) {
      newErrors.hotel_name = "Hotel name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save handler for add/edit modal
  const handleSave = async (formData: User) => {
    if (!validateForm()) {
      return;
    }

    // Ensure hotel_name is set for Manager role when user is Hotel Master
    const dataToSubmit = { ...formData };

    // Make sure hotel_name is not empty for any role
    if (!dataToSubmit.hotel_name) {
      setErrors((prev) => ({
        ...prev,
        hotel_name: "Hotel name is required",
      }));
      return;
    }

    console.log("Submitting data:", dataToSubmit);

    try {
      if (modalType === "edit" && formData.id) {
        await userApi.editUser(formData.id.toString(), dataToSubmit);
        toast.success("User updated successfully!");
      } else if (modalType === "add") {
        await userApi.addUser(dataToSubmit);
        toast.success("User added successfully!");
      }
      fetchUsers();
      closeModal();
    } catch (error: any) {
      console.error("Error saving user:", error);

      // Check if the error has a response with data
      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error("Error saving user. Please try again.");
      }

      // If it's a role not found error, highlight the role field
      if (
        error.response &&
        error.response.data &&
        error.response.data.message === "Role not found"
      ) {
        setErrors((prev) => ({
          ...prev,
          role_name: "Selected role does not exist in the database",
        }));
      }
    }
  };

  // Delete modal management
  const openDeleteConfirmModal = (id: number) => {
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  const handleDelete = async () => {
    if (!deletingId) return;
    try {
      await userApi.deleteUser(deletingId.toString());
      toast.success("User deleted successfully");
      fetchUsers();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Failed to delete user");
      console.error("Error deleting user:", error);
    }
  };

  const paginatedUsers = sortedUsers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
    
  );

  return (
    <>
      <PageMeta
        title="User Management | Admin Panel"
        description="Manage users in the system"
      />
      <PageBreadcrumb pageTitle="User Management" />

      <ComponentCard title="User List">
        <TableControls
          search={search}
          onSearchChange={handleSearch}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          onAddClick={() => openModal("add")}
          addButtonLabel="Add User"
        />
        <div
          className="w-full overflow-hidden"
          style={{ position: "relative" }}
        >
          <GenericTable
            data={paginatedUsers}
            columns={[
              {
                header: "User",
                accessor: "first_name",
                cell: (item) => {
                  // Generate a random color based on the user's name
                  const getAvatarColor = (name: string) => {
                    const colors = [
                      "bg-purple-500",
                      "bg-pink-500",
                      "bg-blue-500",
                      "bg-green-500",
                      "bg-yellow-500",
                      "bg-red-500",
                      "bg-indigo-500",
                      "bg-orange-500",
                    ];
                    // Use the sum of character codes to determine the color
                    const charCodeSum = name
                      .split("")
                      .reduce((sum, char) => sum + char.charCodeAt(0), 0);
                    return colors[charCodeSum % colors.length];
                  };

                  // Get initials from first and last name
                  const getInitials = (firstName: string, lastName: string) => {
                    return `${firstName.charAt(0)}${lastName.charAt(
                      0
                    )}`.toUpperCase();
                  };

                  const avatarColor = getAvatarColor(
                    item.first_name + item.last_name
                  );
                  const initials = getInitials(item.first_name, item.last_name);

                  return (
                    <div
                      className="flex items-center"
                      style={{ marginLeft: "-11px" }}
                    >
                      <div
                        className={`flex-shrink-0 h-10 w-10 rounded-full ${avatarColor} flex items-center justify-center text-white font-medium`}
                      >
                        {initials}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {item.first_name} {item.last_name}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {item.email}
                        </div>
                      </div>
                    </div>
                  );
                },
                sortable: true,
              },
              {
                header: "Phone",
                accessor: "phone",
                cell: (item) => (
                  <div
                    className=" mr-0.5 text-sm text-gray-700 dark:text-gray-300"
                    style={{ marginLeft: "-9px" }}
                  >
                    {item.phone}
                  </div>
                ),
              },
              {
                header: "Role",
                accessor: "role_name",
                cell: (item) => {
                  const roleName = item.Role?.role_name || item.role_name;
                  let bgColor = "bg-blue-100 text-blue-800";

                  // Different colors for different roles
                  if (roleName.toLowerCase() === "admin") {
                    bgColor = "bg-red-100 text-red-800";
                  } else if (roleName.toLowerCase() === "hotel master") {
                    bgColor = "bg-green-100 text-green-800";
                  } else if (roleName.toLowerCase() === "manager") {
                    bgColor = "bg-purple-100 text-purple-800";
                  }

                  return (
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-4 ${bgColor}`}
                      style={{ marginLeft: "-9px" }}
                    >
                      {roleName}
                    </span>
                  );
                },
              },
              {
                header: "Hotel Name",
                accessor: "hotel_name",
                cell: (item) => {
                  // Get hotel name from either hotel_name or Hotel.Hotel_name
                  const hotelName =
                    item.Hotel?.Hotel_name || item.hotel_name || "-";
                  return (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {hotelName}
                    </div>
                  );
                },
              },
            ]}
            onEdit={(user) => openModal("edit", user)}
            onDelete={(id) => openDeleteConfirmModal(id)}
            startIndex={startingSerialNumber}
            onSort={handleSort}
            sortField={sortField}
            sortDirection={sortDirection}
            idField="id"
            showCheckboxes={true}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
          />
        </div>

        {totalItems > 0 && (
          <div className="mt-2">
            <Pagination
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              startingSerialNumber={startingSerialNumber}
            />
          </div>
        )}
      </ComponentCard>

      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit User" : "Add User"}
        onSubmit={(e) => {
          e.preventDefault();
          // If editing, include the ID
          const submitData = { ...formData };
          if (selectedUser?.id) {
            submitData.id = selectedUser.id;
          }

          // Make sure hotel_name is not empty
          if (!submitData.hotel_name) {
            setErrors((prev) => ({
              ...prev,
              hotel_name: "Hotel name is required",
            }));
            return;
          }

          console.log("Form submit data:", submitData);
          handleSave(submitData);
        }}
      >
        <div className="grid grid-cols-2 gap-4 mb-4">
          {/* First Name */}
          <div className="col-span-1">
            <Label>
              First Name <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="text"
              name="first_name"
              value={formData.first_name}
              onChange={(e) =>
                setFormData({ ...formData, first_name: e.target.value })
              }
              error={!!errors.first_name}
              hint={errors.first_name || ""}
            />
          </div>

          {/* Last Name */}
          <div className="col-span-1">
            <Label>
              Last Name <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="text"
              name="last_name"
              value={formData.last_name}
              onChange={(e) =>
                setFormData({ ...formData, last_name: e.target.value })
              }
              error={!!errors.last_name}
              hint={errors.last_name || ""}
            />
          </div>

          {/* Phone */}
          <div className="col-span-1">
            <Label>
              Phone <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="text"
              name="phone"
              value={formData.phone}
              onChange={(e) =>
                setFormData({ ...formData, phone: e.target.value })
              }
              error={!!errors.phone}
              maxLength={10}
              hint={errors.phone || ""}
            />
          </div>

          {/* Email */}
          <div className="col-span-1">
            <Label>
              Email <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="email"
              name="email"
              value={formData.email}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              error={!!errors.email}
              hint={errors.email || ""}
            />
          </div>

          {/* Password */}
          <div className="col-span-1">
            <Label>
              Password <span className="text-black dark:text-white">*</span>
            </Label>
            <div className="relative">
              <Input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={(e) =>
                  setFormData({ ...formData, password: e.target.value })
                }
                error={!!errors.password}
                hint={errors.password || ""}
              />
              <button
                type="button"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 focus:outline-none"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff size={18} /> : <Eye size={18} />}
              </button>
            </div>
          </div>

          {/* Role */}
          <div className="col-span-1">
            <Label>
              Role <span className="text-black dark:text-white">*</span>
            </Label>
            <select
              name="role_name"
              value={formData.role_name}
              onChange={(e) => {
                setFormData({ ...formData, role_name: e.target.value });
              }}
              className={`w-full p-3 border ${
                errors.role_name ? "border-red-500" : "border-gray-300"
              } rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
              disabled={currentUserRole.toLowerCase() === "hotel master"}
            >
              <option value="">Select Role</option>
              {roles
                .filter((role) => {
                  // If current user is Hotel Master or Manager, only show Manager role
                  if (currentUserRole.toLowerCase() === "hotel master" || currentUserRole.toLowerCase() === "manager") {
                    return role.role_name.toLowerCase() === "manager";
                  }
                  // For Admin, show all roles
                  return true;
                })
                .map((role) => (
                  <option key={role.id} value={role.role_name}>
                    {role.role_name}
                  </option>
                ))}
            </select>
            {errors.role_name && (
              <p className="mt-1 text-sm text-red-500">{errors.role_name}</p>
            )}
          </div>

          {/* Hotel Name */}
          <div className="col-span-1">
            <Label>
              Hotel Name <span className="text-black dark:text-white">*</span>
            </Label>
            <div className="relative">
    
              
              {/* Conditional rendering based on user role */}
              {(currentUserRole.toLowerCase() === "hotel master" || 
                currentUserRole.toLowerCase() === "manager") ? (
                // For Hotel Master or Manager: Show a disabled select with their hotel prefilled
                <select
                  name="hotel_name"
                  value={currentUserHotelName || ""}
                  disabled={true}
                  className={`w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white appearance-none bg-gray-100`}
                >
                  <option value={currentUserHotelName}>{currentUserHotelName}</option>
                </select>
              ) : (
                // For Admin: Show the normal dropdown
                <select
                  name="hotel_name"
                  value={formData.hotel_name || ""}
                  onChange={(e) =>
                    setFormData({ ...formData, hotel_name: e.target.value })
                  }
                  className={`w-full p-3 border ${
                    errors.hotel_name ? "border-red-500" : "border-gray-300"
                  } rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white appearance-none`}
                >
                  <option value="">Select Hotel</option>
                  {isLoadingHotels ? (
                    <option value="" disabled>
                      Loading hotels...
                    </option>
                  ) : (
                    hotels.map((hotel) => (
                      <option key={hotel.id} value={hotel.Hotel_name}>
                        {hotel.Hotel_name}
                      </option>
                    ))
                  )}
                </select>
              )}
              
              {/* Arrow indicator for dropdown */}
              <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                <svg
                  className="fill-current h-4 w-4"
                  xmlns="http://www.w3.org/2000/svg"
                  viewBox="0 0 20 20"
                >
                  <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                </svg>
              </div>
              {errors.hotel_name && (
                <p className="mt-1 text-sm text-red-500">{errors.hotel_name}</p>
              )}
            </div>
          </div>
        </div>
      </GenericAddEditModal>

      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        UserName={
          users.find((user) => user.id === deletingId)?.first_name +
          " " +
          users.find((user) => user.id === deletingId)?.last_name
        }
      />
    </>
  );
};

export default UserManagement;
