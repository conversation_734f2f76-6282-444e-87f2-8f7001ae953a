import { useState, useEffect } from "react";
import axios from "axios";
import { useModal } from "../../hooks/useModal";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "../../context/AuthContext";
import { Pencil, Plus, Trash2 } from "lucide-react";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Input from "../form/input/InputField";
import Label from "../form/Label";

interface CustomField {
  id?: number;
  field_key: string;
  field_value: string;
}

interface CustomFieldsProps {
  hotelId: number;
}

export default function CustomFields({ hotelId }: CustomFieldsProps) {
  const { TokenFROMLSGet } = useAuth();
  const { isOpen: isFieldModalOpen, openModal: openFieldModal, closeModal: closeFieldModal } = useModal();
  const { isOpen: isDeleteModalOpen, openModal: openDeleteModal, closeModal: closeDeleteModal } = useModal();

  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [fieldFormData, setFieldFormData] = useState<CustomField>({
    field_key: "",
    field_value: ""
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [selectedFieldId, setSelectedFieldId] = useState<number | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (hotelId) {
      fetchCustomFields();
    }
  }, [hotelId]);

  const fetchCustomFields = async () => {
    try {
      const token = TokenFROMLSGet();

      const response = await axios.get(`http://localhost:3000/api/users/${hotelId}/custom-fields`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (response.data && response.data.customFields) {
        setCustomFields(response.data.customFields);
      }
    } catch (error) {
      console.error("Error fetching custom fields:", error);
      setCustomFields([]);
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!fieldFormData.field_key.trim()) {
      newErrors.field_key = "Field name is required";
    }

    if (!fieldFormData.field_value.trim()) {
      newErrors.field_value = "Field value is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveField = async (formData: CustomField) => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      const token = TokenFROMLSGet();

      if (isEditing && selectedFieldId) {
        // Update existing field
        await axios.put(
          `http://localhost:3000/api/users/${hotelId}/custom-fields/${selectedFieldId}`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        toast.success("Custom field updated successfully");
      } else {
        // Create new field
        await axios.post(
          `http://localhost:3000/api/users/${hotelId}/custom-fields`,
          formData,
          {
            headers: {
              Authorization: `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          }
        );
        toast.success("Custom field added successfully");
      }

      fetchCustomFields();
      closeFieldModal();
    } catch (error) {
      console.error("Error saving custom field:", error);
      toast.error("Failed to save custom field");
    }
  };

  const handleDeleteField = async () => {
    if (!selectedFieldId) {
      return;
    }

    try {
      const token = TokenFROMLSGet();

      await axios.delete(
        `http://localhost:3000/api/users/${hotelId}/custom-fields/${selectedFieldId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      toast.success("Custom field deleted successfully");
      closeDeleteModal();
      fetchCustomFields();
    } catch (error) {
      console.error("Error deleting custom field:", error);
      toast.error("Failed to delete custom field");
    }
  };

  const openAddModal = () => {
    setIsEditing(false);
    setSelectedFieldId(null);
    setFieldFormData({
      field_key: "",
      field_value: ""
    });
    setErrors({});
    openFieldModal();
  };

  const openEditFieldModal = (field: CustomField) => {
    setIsEditing(true);
    setSelectedFieldId(field.id || null);
    setFieldFormData({
      field_key: field.field_key,
      field_value: field.field_value
    });
    setErrors({});
    openFieldModal();
  };

  const openDeleteFieldModal = (fieldId: number) => {
    setSelectedFieldId(fieldId);
    openDeleteModal();
  };

  return (
    <>
      <div className="p-6 bg-white rounded-xl border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-800">
            Custom Fields
          </h3>
          <button
            onClick={openAddModal}
            className="inline-flex items-center justify-center rounded-full border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700"
          >
            <Plus size={16} className="mr-1" />
            <span>Add Field</span>
          </button>
        </div>

        {customFields.length > 0 ? (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <tbody className="bg-white divide-y divide-gray-200">
                {customFields.map((field) => (
                  <tr key={field.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {field.field_key}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {field.field_value}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => openEditFieldModal(field)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          <Pencil size={16} />
                        </button>
                        <button
                          onClick={() => openDeleteFieldModal(field.id!)}
                          className="text-red-600 hover:text-red-900"
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="py-8 text-center text-gray-500 bg-gray-50 rounded-lg">
            <p className="mb-2 text-lg font-medium">No custom fields added yet</p>
            <p className="text-sm">Add custom fields like pincode, address, or any other information related to your hotel.</p>
          </div>
        )}
      </div>

      {/* Add/Edit Field Modal */}
      <GenericAddEditModal
        isOpen={isFieldModalOpen}
        onClose={closeFieldModal}
        title={isEditing ? "Edit Custom Field" : "Add New Custom Field"}
        onSubmit={(e) => {
          e.preventDefault();
          handleSaveField(fieldFormData);
        }}
      >
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <Label>Field Name <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="field_key"
              placeholder="e.g., Pincode, Address, etc."
              value={fieldFormData.field_key}
              onChange={(e) => {
                const newValue = e.target.value;
                setFieldFormData({...fieldFormData, field_key: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setErrors({...errors, field_key: ""});
                } else {
                  setErrors({...errors, field_key: "Field name is required"});
                }
              }}
              error={!!errors.field_key}
              hint={errors.field_key || ""}
            />
          </div>

          <div>
            <Label>Field Value <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="field_value"
              placeholder="e.g., 383001, Main Street, etc."
              value={fieldFormData.field_value}
              onChange={(e) => {
                const newValue = e.target.value;
                setFieldFormData({...fieldFormData, field_value: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setErrors({...errors, field_value: ""});
                } else {
                  setErrors({...errors, field_value: "Field value is required"});
                }
              }}
              error={!!errors.field_value}
              hint={errors.field_value || ""}
            />
          </div>
        </div>
      </GenericAddEditModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDeleteField}
        title="Confirm Deletion"
        message="Are you sure you want to delete this custom field? This action cannot be undone."
        UserName="this custom field"
      />
    </>
  );
}
