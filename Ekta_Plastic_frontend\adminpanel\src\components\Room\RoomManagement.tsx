import React, { useEffect, useState, useRef } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { ChevronUp, ChevronDown, X, Eye } from "lucide-react";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import GenericViewModal from "../ViewModel/GenericViewModal";
import { TableControls } from "../common/CrudControls";

interface Room {
  id?: number;
  hotel_id?: number;
  room_type_id?: number;
  room_number?: string;
  floor_number?: string;
  status?: "available" | "occupied" | "maintenance";
  description?: string;
  amenities?: number[];
  images?: string[] | { id: number; url: string }[];
  room_type?: string;
  RoomType?: {
    name: string;
  };
  sr_no?: number;
  room_count?: number; // Number of rooms of this type
  removedImageIds?: number[]; // Track image IDs to be removed
  quantity?: number; // Number of rooms to create
  [key: string]: any; // Allow any additional properties
}

interface RoomType {
  id: number;
  name: string;
  description?: string;
  max_occupancy?: number;
}

interface Amenity {
  id: number;
  name: string;
  description?: string;
  status?: boolean;
}

const RoomManagement = () => {
  const [rooms, setRooms] = useState<Room[]>([]);
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([]);
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedRoom, setSelectedRoom] = useState<Room | null>(null);
  const [formData, setFormData] = useState<Room>({
    room_type_id: 0,
    room_number: "",
    floor_number: "",
    status: "available", // Using the enum string value
    description: "",
    amenities: [],
    images: [],
    removedImageIds: [],
    quantity: 1,
  });

  const [roomTypeDetails, setRoomTypeDetails] = useState<any>(null);
  const [viewLoading, setViewLoading] = useState<boolean>(false);
  // Store actual File objects separately
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState<boolean>(false);
  const [isAmenitiesDropdownOpen, setIsAmenitiesDropdownOpen] =
    useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>("room_number");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // State for multiple room details when quantity > 1
  const [multiRoomDetails, setMultiRoomDetails] = useState<
    Array<{
      room_number: string;
      floor_number: string;
      status: "available" | "occupied" | "maintenance";
    }>
  >([]);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Add loading state
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      const [roomsResponse, roomTypesResponse, amenitiesResponse] =
        await Promise.all([
          userApi.getAllRooms(currentPage, itemsPerPage),
          userApi.getAllRoomTypes(),
          userApi.getAllAmenityNames(),
        ]);

      console.log("Room API Response:", roomsResponse.data);

      // Check if the response has the expected structure
      console.log("Processing room data:", roomsResponse.data);

      let roomsData: any[] = [];
      let totalCount = 0;

      if (roomsResponse.data && Array.isArray(roomsResponse.data.rooms)) {
        roomsData = roomsResponse.data.rooms;
        totalCount =
          roomsResponse.data.totalCount || roomsResponse.data.rooms.length || 0;
      } else if (roomsResponse.data && Array.isArray(roomsResponse.data)) {
        roomsData = roomsResponse.data;
        totalCount = roomsResponse.data.length || 0;
      } else if (roomsResponse.data && typeof roomsResponse.data === "object") {
        // Handle the case where data is in the format shown in the screenshot
        if (Array.isArray(roomsResponse.data.rooms)) {
          roomsData = roomsResponse.data.rooms;
          totalCount =
            roomsResponse.data.total || roomsResponse.data.rooms.length;
        } else {
          // Try to extract rooms array from the object
          const possibleRoomsArray = Object.entries(roomsResponse.data).find(
            ([key, value]) => Array.isArray(value) && key !== "total"
          );

          if (possibleRoomsArray && Array.isArray(possibleRoomsArray[1])) {
            roomsData = possibleRoomsArray[1] as any[];
            totalCount = (roomsResponse.data as any).total || roomsData.length;
          }
        }
      } else {
        console.error("Unexpected room data structure:", roomsResponse.data);
      }

      // Process each room to ensure it has an id property and properly formatted sample_image
      const processedRooms = roomsData.map((room: any, index: number) => {
        // Create a processed room object
        let processedRoom = { ...room };

        // If room doesn't have an id, use sr_no or index+1 as id
        if (processedRoom.id === undefined) {
          processedRoom.id = processedRoom.sr_no || index + 1;
        }

        // Process sample_image to ensure it's properly formatted
        if (processedRoom.sample_image) {
          console.log("Original sample_image:", processedRoom.sample_image);

          // If it's not a full URL, make it absolute
          if (!processedRoom.sample_image.startsWith("http")) {
            // Remove any leading slashes
            const cleanUrl = processedRoom.sample_image.replace(/^\/+/, "");
            processedRoom.sample_image = `http://localhost:3000/${cleanUrl}`;
            console.log(
              "Updated sample_image URL:",
              processedRoom.sample_image
            );
          }
        }

        // Process images array if it exists
        if (processedRoom.images && Array.isArray(processedRoom.images)) {
          console.log(
            `Room ${processedRoom.id} has ${processedRoom.images.length} images`
          );

          // Process each image URL in the array
          processedRoom.images = processedRoom.images.map((img: any) => {
            if (typeof img === "string") {
              // If it's not a full URL, make it absolute
              if (!img.startsWith("http")) {
                const cleanUrl = img.replace(/^\/+/, "");
                return `http://localhost:3000/${cleanUrl}`;
              }
              return img;
            } else if (img && typeof img === "object" && "url" in img) {
              // If it's an object with a URL property
              let imgUrl = img.url;
              if (!imgUrl.startsWith("http")) {
                const cleanUrl = imgUrl.replace(/^\/+/, "");
                img.url = `http://localhost:3000/${cleanUrl}`;
              }
              return img;
            }
            return img;
          });
        }

        // Make sure room_count is set if we have multiple rooms
        if (
          !processedRoom.room_count &&
          processedRoom.images &&
          Array.isArray(processedRoom.images) &&
          processedRoom.images.length > 1
        ) {
          processedRoom.room_count = processedRoom.images.length;
          console.log(
            `Set room_count to ${processedRoom.room_count} based on images array length`
          );
        }

        return processedRoom;
      });

      console.log("Processed rooms:", processedRooms);

      // Check if rooms have sample_image field
      const hasImages = processedRooms.some((room) => room.sample_image);
      console.log("Rooms have sample_image field:", hasImages);
      if (hasImages) {
        console.log("Sample image example:", processedRooms[0]?.sample_image);
      }

      setRooms(processedRooms);
      setTotalItems(totalCount);

      if (roomTypesResponse.data && roomTypesResponse.data.roomTypes) {
        setRoomTypes(roomTypesResponse.data.roomTypes);
      }

      if (amenitiesResponse.data && amenitiesResponse.data.amenityNames) {
        setAmenities(amenitiesResponse.data.amenityNames);
        console.log("Amenities loaded:", amenitiesResponse.data.amenityNames);
      } else {
        console.error(
          "Amenities data structure is incorrect:",
          amenitiesResponse.data
        );
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      toast.error("Error fetching data");
      console.error("Error fetching data:", error);
    }
  };

  const fetchRoomDetails = async (roomTypeName: string) => {
    try {
      setViewLoading(true);
      const response = await userApi.viewRoomsByRoomTypeName(roomTypeName);
      console.log("Room details response:", response.data);

      if (response.data) {
        setRoomTypeDetails(response.data);
      }
    } catch (error) {
      console.error("Error fetching room details:", error);
      toast.error("Failed to load room details");
    } finally {
      setViewLoading(false);
    }
  };
  // Initial data loading
  useEffect(() => {
    fetchData();
    // Set default sorting to room_number ascending
    setSortField("room_number");
    setSortDirection("asc");
  }, []);

  // Reload when pagination or sorting changes
  useEffect(() => {
    fetchData();
  }, [currentPage, itemsPerPage]);

  // Add click outside handler to close amenities dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      // Close amenities dropdown when clicking outside
      if (isAmenitiesDropdownOpen) {
        const target = event.target as HTMLElement;
        const isClickInsideAmenitiesDropdown = target.closest(
          ".amenities-dropdown-container"
        );
        if (!isClickInsideAmenitiesDropdown) {
          setIsAmenitiesDropdownOpen(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isAmenitiesDropdownOpen]);

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // Filter rooms based on search term
  const filteredRooms = rooms.filter((room) => {
    if (!room) return false;

    const searchLower = search.toLowerCase();
    return (
      (room.room_number &&
        room.room_number.toString().toLowerCase().includes(searchLower)) ||
      (room.description &&
        room.description.toString().toLowerCase().includes(searchLower)) ||
      (room.room_type &&
        room.room_type.toString().toLowerCase().includes(searchLower)) ||
      (room.RoomType?.name &&
        room.RoomType.name.toString().toLowerCase().includes(searchLower))
    );
  });

  // Sort the filtered rooms
  const sortedRooms = [...filteredRooms].sort((a, b) => {
    if (!sortField) return 0;

    if (sortField === "room_number") {
      // Try to convert room numbers to numbers for numeric sorting
      const roomNumberA = a.room_number ? a.room_number.toString() : "";
      const roomNumberB = b.room_number ? b.room_number.toString() : "";

      // Check if both room numbers are numeric
      const numA = parseInt(roomNumberA);
      const numB = parseInt(roomNumberB);

      if (!isNaN(numA) && !isNaN(numB)) {
        // If both are valid numbers, sort numerically
        return sortDirection === "asc" ? numA - numB : numB - numA;
      } else {
        // Otherwise, sort alphabetically
        return sortDirection === "asc"
          ? roomNumberA.localeCompare(roomNumberB)
          : roomNumberB.localeCompare(roomNumberA);
      }
    }

    if (sortField === "floor_number") {
      const floorA = (a.floor_number || "").toString().toLowerCase();
      const floorB = (b.floor_number || "").toString().toLowerCase();
      return sortDirection === "asc"
        ? floorA.localeCompare(floorB)
        : floorB.localeCompare(floorA);
    }

    // For room_type sorting
    if (sortField === "room_type_id" || sortField === "room_type") {
      const typeA = a.room_type || a.RoomType?.name || "";
      const typeB = b.room_type || b.RoomType?.name || "";
      return sortDirection === "asc"
        ? typeA.toString().localeCompare(typeB.toString())
        : typeB.toString().localeCompare(typeA.toString());
    }

    return 0;
  });

const paginatedRooms = sortedRooms.slice(
  ((currentPage || 1) - 1) * (itemsPerPage || sortedRooms.length),
  (currentPage || 1) * (itemsPerPage || sortedRooms.length)
);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    let newDirection: "asc" | "desc";

    if (sortField === field) {
      newDirection = sortDirection === "asc" ? "desc" : "asc";
      setSortDirection(newDirection);
    } else {
      newDirection = "asc";
      setSortField(field);
      setSortDirection(newDirection);
    }

    // Manually sort the data since we removed the dependency from useEffect
    console.log(`Sorting by ${field} in ${newDirection} order`);
  };

  // Open view modal
  // Open view modal
  const openViewModal = (room: Room) => {
    console.log("Opening view modal for room:", room);
    setSelectedRoom(room);
    if (room && room.room_type) {
      fetchRoomDetails(room.room_type);
    }
    setIsViewModalOpen(true);
  };

  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedRoom(null);
  };

  // Open modal for add or edit operation
  const openModal = (type: "add" | "edit", room: Room | null = null) => {
    setModalType(type);
    setSelectedRoom(room);
    setErrors({});

    // Clear selected files when opening a new modal
    setSelectedFiles([]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    if (room) {
      console.log("Editing room:", room);

      // Convert room_type to room_type_id if needed
      let roomTypeId = room.room_type_id;
      if (!roomTypeId && room.room_type && roomTypes.length > 0) {
        const foundType = roomTypes.find(
          (type) => type.name === room.room_type
        );
        if (foundType) {
          roomTypeId = foundType.id;
        }
      }

      // Parse amenities if it's a string
      let amenitiesList: number[] = [];
      if (room.amenities) {
        if (typeof room.amenities === "string") {
          // Try to match amenity names to IDs
          const amenityNames = (room.amenities as string)
            .split(",")
            .map((a: string) => a.trim());
          if (amenities.length > 0) {
            amenitiesList = amenities
              .filter((a) => amenityNames.includes(a.name))
              .map((a) => a.id);
          }
        } else if (Array.isArray(room.amenities)) {
          amenitiesList = room.amenities;
        }
      }

      // Process images to ensure they're in the right format
      let processedImages: any[] = [];
      let originalImageData: { id: number; url: string }[] = [];

      if (room.images) {
        if (Array.isArray(room.images)) {
          // Store the original image data with IDs for reference
          originalImageData = room.images
            .filter(
              (img) =>
                img && typeof img === "object" && "id" in img && "url" in img
            )
            .map((img) => ({
              id: (img as { id: number; url: string }).id,
              url: (img as { id: number; url: string }).url,
            }));

          console.log("Original image data with IDs:", originalImageData);

          // Process images for display
          processedImages = room.images.map((img) => {
            if (typeof img === "string") {
              return img;
            } else if (
              img &&
              typeof img === "object" &&
              "url" in img &&
              "id" in img
            ) {
              // Store as an object with both id and url
              const imageObj = {
                id: (img as { id: number; url: string }).id,
                url: (img as { id: number; url: string }).url,
              };

              // If it's a relative URL, make it absolute for display
              if (
                !imageObj.url.startsWith("http") &&
                !imageObj.url.startsWith("data:")
              ) {
                // Remove any leading slashes
                const cleanUrl = imageObj.url.replace(/^\/+/, "");
                imageObj.url = `http://localhost:3000/${cleanUrl}`;
              }

              return imageObj;
            }
            return img;
          });
        }
      }

      console.log("Processed images for edit:", processedImages);

      // For editing an existing room
      setFormData({
        id: room.id,
        room_type_id: roomTypeId || 0,
        room_number: room.room_number || "",
        floor_number: room.floor_number || "",
        status: room.status,
        description: room.description || "",
        amenities: amenitiesList,
        images: processedImages,
        removedImageIds: [],
      });
    } else {
      // For adding a new room - reset form with empty values
      setFormData({
        room_type_id: 0,
        room_number: "",
        floor_number: "",
        status: "available",
        description: "",
        amenities: [],
        images: [],
      });
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedRoom(null);
    setModalType("");
    setIsAmenitiesDropdownOpen(false);

    // Clear selected files when closing the modal
    setSelectedFiles([]);

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Validate form data with provided data
  const validateFormData = (data: Room) => {
    const newErrors: { [key: string]: string } = {};

    console.log("Validating form data:", data);

    // Room Type validation
    if (!data.room_type_id) {
      newErrors.room_type_id = "Room type is required";
      console.log("Room type validation failed");
    }

    // Quantity validation for add mode
    if (modalType === "add") {
      const quantity = data.quantity || 1;
      if (quantity < 1) {
        newErrors.quantity = "Quantity must be at least 1";
      } else if (quantity > 100) {
        newErrors.quantity = "Quantity cannot exceed 100";
      }
    }

    // Room Number validation - only required if quantity is 1
    if (modalType === "add" && (data.quantity || 0) <= 1) {
      if (!data.room_number) {
        newErrors.room_number = "Room number is required";
      } else if (data.room_number.trim() === "") {
        newErrors.room_number = "Room number cannot be empty";
      }
    } else if (modalType === "edit") {
      if (!data.room_number) {
        newErrors.room_number = "Room number is required";
      } else if (data.room_number.trim() === "") {
        newErrors.room_number = "Room number cannot be empty";
      }
    } else if (modalType === "add" && (data.quantity || 0) > 1) {
      // Validate multiRoomDetails when quantity > 1
      let hasEmptyRoomNumber = false;
      let hasEmptyFloorNumber = false;

      multiRoomDetails.forEach((room) => {
        if (!room.room_number || room.room_number.trim() === "") {
          hasEmptyRoomNumber = true;
        }
        if (!room.floor_number || room.floor_number.trim() === "") {
          hasEmptyFloorNumber = true;
        }
      });

      if (hasEmptyRoomNumber) {
        newErrors.multiRoomNumbers = "All room numbers are required";
      }

      if (hasEmptyFloorNumber) {
        newErrors.multiFloorNumbers = "All floor numbers are required";
      }
    }

    // Floor Number validation - only check if not using multiRoomDetails
    if (
      modalType === "edit" ||
      (modalType === "add" && (data.quantity || 0) <= 1)
    ) {
      const floorNumber = data.floor_number || "";
      if (!floorNumber.toString().trim()) {
        newErrors.floor_number = "Floor number is required";
      }
    }

    // Amenities validation (optional)
    if (Array.isArray(data.amenities) && data.amenities.length === 0) {
      newErrors.amenities = "Please select at least one amenity";
    }

    // Description validation
    if (!data.description || data.description.trim() === "") {
      newErrors.description = "Description is required";
    }

    // Images validation (optional for edit, but required for add)
    if (modalType === "add" && (!data.images || data.images.length === 0)) {
      newErrors.images = "Please upload at least one image";
    }

    setErrors(newErrors);

    // Log validation result
    const isValid = Object.keys(newErrors).length === 0;
    console.log(
      "Form validation result:",
      isValid ? "VALID" : "INVALID",
      newErrors
    );

    return isValid;
  };

  // Save handler for add/edit modal
  const handleSave = async (formDataToSave: Room) => {
    console.log("handleSave called with data:", formDataToSave);

    // Update the formData state with the submitted data
    setFormData(formDataToSave);

    // Direct validation without relying on state update
    const isValid = validateFormData(formDataToSave);
    if (!isValid) {
      console.log("Form validation failed");
      return;
    }

    console.log("Form validation passed, proceeding with save");

    try {
      // Use the passed formDataToSave instead of the state formData
      if (modalType === "edit" && formDataToSave.id) {
        console.log("Editing room with ID:", formDataToSave.id);
        // For editing, create a single FormData object
        const formDataToSubmit = new FormData();

        // Add all form fields to FormData (hotel_id will be extracted from JWT token in backend)
        formDataToSubmit.append(
          "room_type_id",
          (formDataToSave.room_type_id || 0).toString()
        );
        formDataToSubmit.append(
          "room_number",
          formDataToSave.room_number || ""
        );
        formDataToSubmit.append(
          "floor_number",
          (formDataToSave.floor_number || "").toString()
        );

        // Handle status field
        const statusValue = formDataToSave.status || "available";
        formDataToSubmit.append("status", statusValue);

        formDataToSubmit.append(
          "description",
          formDataToSave.description || ""
        );

        // Add amenities as JSON string
        const amenitiesValue = Array.isArray(formDataToSave.amenities)
          ? formDataToSave.amenities
          : typeof formDataToSave.amenities === "string"
          ? (formDataToSave.amenities as string)
              .split(",")
              .map((a: string) => a.trim())
          : [];
        formDataToSubmit.append("amenities", JSON.stringify(amenitiesValue));

        // Add all stored File objects to the form data
        if (selectedFiles.length > 0) {
          console.log(
            `Adding ${selectedFiles.length} files to form data for edit`
          );
          selectedFiles.forEach((file) => {
            formDataToSubmit.append("images", file);
          });
        } else if (
          fileInputRef.current &&
          fileInputRef.current.files &&
          fileInputRef.current.files.length > 0
        ) {
          // Fallback to using the file input directly if no stored files
          console.log(
            `Using file input directly: ${fileInputRef.current.files.length} files for edit`
          );
          const files = fileInputRef.current.files;
          for (let i = 0; i < files.length; i++) {
            formDataToSubmit.append("images", files[i]);
          }
        }

        // Log the FormData contents for debugging
        console.log("FormData entries for edit room:");
        for (const pair of (formDataToSubmit as any).entries()) {
          if (pair[0] === "images") {
            console.log(pair[0], "File:", pair[1].name);
          } else {
            console.log(pair[0], pair[1]);
          }
        }

        // Add existing image URLs if editing
        if (Array.isArray(formDataToSave.images)) {
          // Process existing images
          const existingImageUrls: string[] = [];
          const existingImageIds: number[] = [];

          formDataToSave.images.forEach((img) => {
            if (typeof img === "string") {
              existingImageUrls.push(img);
            } else if (img && typeof img === "object") {
              if ("url" in img && "id" in img) {
                // Skip blob URLs (they'll be uploaded as files)
                if (!(img as any).url.startsWith("blob:")) {
                  existingImageUrls.push((img as any).url);
                  existingImageIds.push((img as any).id);
                }
              }
            }
          });

          if (existingImageUrls.length > 0) {
            formDataToSubmit.append(
              "existingImages",
              JSON.stringify(existingImageUrls)
            );
          }

          if (existingImageIds.length > 0) {
            formDataToSubmit.append(
              "existingImageIds",
              JSON.stringify(existingImageIds)
            );
          }

          // Add image IDs to be removed
          if (
            formDataToSave.removedImageIds &&
            formDataToSave.removedImageIds.length > 0
          ) {
            console.log(
              "Sending removed image IDs:",
              formDataToSave.removedImageIds
            );
            formDataToSubmit.append(
              "remove_images",
              JSON.stringify(formDataToSave.removedImageIds)
            );
          }
        }

        // Update the room
        try {
          console.log(
            "Sending API request to update room with ID:",
            formDataToSave.id
          );
          const response = await userApi.updateRoom(
            formDataToSave.id.toString(),
            formDataToSubmit
          );
          console.log("API update response:", response);
          toast.success("Room updated successfully!");
          closeModal();
          fetchData();
        } catch (error) {
          console.error("API update error:", error);
          toast.error("Failed to update room. See console for details.");
          throw error; // Re-throw to be caught by the outer try-catch
        }
      } else if (modalType === "add") {
        // For adding, handle quantity
        const quantity = formDataToSave.quantity || 1;
        console.log("Adding room with quantity:", quantity);

        if (quantity === 1) {
          // Single room creation
          const formDataToSubmit = new FormData();

          // Add all form fields to FormData (hotel_id will be extracted from JWT token in backend)
          formDataToSubmit.append(
            "room_type_id",
            (formDataToSave.room_type_id || 0).toString()
          );
          formDataToSubmit.append(
            "room_number",
            formDataToSave.room_number || ""
          );
          formDataToSubmit.append(
            "floor_number",
            (formDataToSave.floor_number || "").toString()
          );

          // Handle status field
          const statusValue = formDataToSave.status || "available";
          formDataToSubmit.append("status", statusValue);

          formDataToSubmit.append(
            "description",
            formDataToSave.description || ""
          );

          // Add amenities as JSON string
          const amenitiesValue = Array.isArray(formDataToSave.amenities)
            ? formDataToSave.amenities
            : typeof formDataToSave.amenities === "string"
            ? (formDataToSave.amenities as string)
                .split(",")
                .map((a: string) => a.trim())
            : [];
          formDataToSubmit.append("amenities", JSON.stringify(amenitiesValue));

          // Add all stored File objects to the form data
          if (selectedFiles.length > 0) {
            console.log(
              `Adding ${selectedFiles.length} files to form data for new room`
            );
            selectedFiles.forEach((file) => {
              formDataToSubmit.append("images", file);
            });
          } else if (
            fileInputRef.current &&
            fileInputRef.current.files &&
            fileInputRef.current.files.length > 0
          ) {
            console.log(
              `Using file input directly: ${fileInputRef.current.files.length} files for new room`
            );
            const files = fileInputRef.current.files;
            for (let i = 0; i < files.length; i++) {
              formDataToSubmit.append("images", files[i]);
            }
          }

          // Log the FormData contents for debugging
          console.log("FormData entries for new room:");
          for (const pair of (formDataToSubmit as any).entries()) {
            if (pair[0] === "images") {
              console.log(pair[0], "File:", pair[1].name);
            } else {
              console.log(pair[0], pair[1]);
            }
          }

          // Create the room
          try {
            console.log("Sending API request to create room");
            const response = await userApi.createRoom(formDataToSubmit);
            console.log("API response:", response);
            toast.success("Room added successfully!");
          } catch (error) {
            console.error("API error:", error);
            toast.error("Failed to add room. See console for details.");
            throw error; // Re-throw to be caught by the outer try-catch
          }
        } else {
          // Multiple room creation - using a single API call with all rooms
          console.log("Creating multiple rooms:", multiRoomDetails.length);

          // Create a single FormData object for all rooms
          const formDataToSubmit = new FormData();

          // Add common fields that apply to all rooms
          formDataToSubmit.append(
            "room_type_id",
            (formDataToSave.room_type_id || 0).toString()
          );

          formDataToSubmit.append(
            "description",
            formDataToSave.description || ""
          );

          // Add amenities as JSON string
          const amenitiesValue = Array.isArray(formDataToSave.amenities)
            ? formDataToSave.amenities
            : typeof formDataToSave.amenities === "string"
            ? (formDataToSave.amenities as string)
                .split(",")
                .map((a: string) => a.trim())
            : [];
          formDataToSubmit.append("amenities", JSON.stringify(amenitiesValue));

          // Create an array of room details with room_number, floor_number, and status pairs
          const roomDetailsArray = multiRoomDetails.map((roomDetail) => ({
            room_number: roomDetail.room_number,
            floor_number: roomDetail.floor_number,
            status: roomDetail.status,
          }));

          // Log the room details array for debugging
          console.log(
            "Room details array for multiple rooms:",
            roomDetailsArray
          );

          // Check if any room details are missing
          const hasEmptyRoomDetails = roomDetailsArray.some(
            (room) => !room.room_number || !room.floor_number
          );

          if (hasEmptyRoomDetails) {
            console.error("Some room details are missing:", roomDetailsArray);
            toast.error("Please fill in all room numbers and floor numbers");
            return;
          }

          // Add the room details array as a JSON string
          formDataToSubmit.append("rooms", JSON.stringify(roomDetailsArray));

          console.log(
            "Room details array being sent to API:",
            roomDetailsArray
          );

          // Add all stored File objects to the form data
          if (selectedFiles.length > 0) {
            selectedFiles.forEach((file) => {
              formDataToSubmit.append("images", file);
            });
          } else if (
            fileInputRef.current &&
            fileInputRef.current.files &&
            fileInputRef.current.files.length > 0
          ) {
            const files = fileInputRef.current.files;
            for (let j = 0; j < files.length; j++) {
              formDataToSubmit.append("images", files[j]);
            }
          }

          // Log the FormData contents for debugging
          console.log("FormData entries for multiple rooms:");
          for (const pair of (formDataToSubmit as any).entries()) {
            if (pair[0] === "images") {
              console.log(pair[0], "File:", pair[1].name);
            } else {
              console.log(pair[0], pair[1]);
            }
          }

          // Create the rooms with a single API call
          try {
            console.log("Sending API request to create multiple rooms");
            const response = await userApi.createRoom(formDataToSubmit);
            console.log("API response for multiple rooms:", response);
            toast.success(
              `${multiRoomDetails.length} rooms added successfully!`
            );
          } catch (error) {
            console.error("Error creating multiple rooms:", error);
            toast.error("Failed to create rooms. See console for details.");
            throw error; // Re-throw to be caught by the outer try-catch
          }
        }

        // Reset form after successful add
        setFormData({
          room_type_id: 0,
          room_number: "",
          floor_number: "",
          status: "available", // Using the enum string value
          description: "",
          amenities: [],
          images: [],
          removedImageIds: [],
          quantity: 1,
        });

        // Clear selected files
        setSelectedFiles([]);

        // Reset file input
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }

        closeModal();
        fetchData();
      }
    } catch (error: any) {
      console.error("Error saving room:", error);

      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error("Error saving room. Please try again.");
      }
    }
  };

  // Delete modal management
  const openDeleteConfirmModal = (id: number) => {
    setSelectedRows([]); // Clear any multi-selection
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const openMultiDeleteConfirmModal = () => {
    setDeletingId(null); // Clear any single selection
    setIsDeleteConfirmOpen(true);
  };

  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  // Handle selected rows from GenericTable
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        // Multiple deletion
        let successCount = 0;
        let errorCount = 0;

        // Process each selected room
        for (const id of selectedRows) {
          try {
            await userApi.deleteRoom(id.toString());
            successCount++;
          } catch (error) {
            console.error(`Error deleting room with ID ${id}:`, error);
            errorCount++;
          }
        }

        // Show appropriate toast messages
        if (successCount > 0) {
          toast.success(`Successfully deleted ${successCount} room(s)`);
        }
        if (errorCount > 0) {
          toast.error(`Failed to delete ${errorCount} room(s)`);
        }

        // Clear selection
        setSelectedRows([]);
      } else if (deletingId) {
        // Single deletion
        await userApi.deleteRoom(deletingId.toString());
        toast.success("Room deleted successfully");
      } else {
        return; // Nothing to delete
      }

      fetchData();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Failed to delete room(s)");
      console.error("Error deleting room(s):", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Room Management | Admin Panel"
        description="Manage rooms in the system"
      />
      <PageBreadcrumb pageTitle="Room Management" />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <ComponentCard title="Room List">
          <TableControls
            search={search}
            onSearchChange={handleSearch}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            onAddClick={() => openModal("add")}
            addButtonLabel="Add Room"
          />
          <div
            className="w-full overflow-hidden"
            style={{ position: "relative" }}
          >
            <GenericTable
              data={paginatedRooms}
              columns={[
                {
                  header: "Room Type",
                  accessor: "room_type_id",
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.room_type || item.RoomType?.name || "-"}
                    </div>
                  ),
                },
                {
                  header: "Room Count",
                  accessor: "room_count",
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.room_count || "-"}
                    </div>
                  ),
                  sortable: true,
                },

                {
                  header: "Amenities",
                  accessor: "amenities",
                  cell: (item) => {
                    // Format amenities to show first one + count
                    let amenitiesText = "-";
                    let amenitiesCount = 0;
                    let firstAmenity = "";

                    if (!item.amenities) {
                      return (
                        <div className="text-sm text-gray-700 dark:text-gray-300">
                          {amenitiesText}
                        </div>
                      );
                    }

                    if (typeof item.amenities === "string") {
                      const amenitiesList = item.amenities
                        .split(",")
                        .map((a: string) => a.trim());
                      amenitiesCount = amenitiesList.length;
                      firstAmenity = amenitiesList[0] || "";
                    } else if (
                      Array.isArray(item.amenities) &&
                      item.amenities.length > 0
                    ) {
                      // If it's an array of IDs, try to convert to names
                      const amenityNames = item.amenities.map((id: number) => {
                        const amenity = amenities.find((a) => a.id === id);
                        return amenity ? amenity.name : id;
                      });
                      amenitiesCount = amenityNames.length;
                      firstAmenity = amenityNames[0] || "";
                    }

                    if (amenitiesCount > 0) {
                      amenitiesText = firstAmenity;
                      if (amenitiesCount > 1) {
                        amenitiesText += ` +${amenitiesCount - 1}`;
                      }
                    }

                    return (
                      <div className="text-sm text-gray-700 dark:text-gray-300">
                        {amenitiesText}
                      </div>
                    );
                  },
                },
                {
                  header: "Images",
                  accessor: "sample_image", // Changed from "images" to "sample_image"
                  cell: (item) => {
                    // Log the entire item for debugging
                    console.log("Room item in table:", item);

                    // Debug the images structure specifically
                    console.log("Images structure:", {
                      hasImagesArray: !!item.images,
                      imagesType: item.images
                        ? typeof item.images
                        : "undefined",
                      isArray: item.images && Array.isArray(item.images),
                      imagesLength:
                        item.images && Array.isArray(item.images)
                          ? item.images.length
                          : 0,
                      firstImageType:
                        item.images &&
                        Array.isArray(item.images) &&
                        item.images.length > 0
                          ? typeof item.images[0]
                          : "none",
                      hasSampleImage: !!item.sample_image,
                      sampleImageType: item.sample_image
                        ? typeof item.sample_image
                        : "undefined",
                    });

                    // Get image URL from sample_image field (primary source based on your API response)
                    let imageUrl = "";
                    let imagesCount = 0;

                    // First check if we have a room_count field to determine multiple rooms
                    if (
                      item.room_count &&
                      typeof item.room_count === "number" &&
                      item.room_count > 1
                    ) {
                      imagesCount = item.room_count; // Use room_count as the image count indicator
                      console.log(
                        `Using room_count (${item.room_count}) as image count indicator`
                      );
                    }

                    // Check for images array to get the image URL and count if not already set
                    if (item.images) {
                      if (
                        Array.isArray(item.images) &&
                        item.images.length > 0
                      ) {
                        // Only update imagesCount if we didn't already set it from room_count
                        if (imagesCount <= 1) {
                          imagesCount = item.images.length;
                        }

                        const firstImage = item.images[0];

                        if (typeof firstImage === "string") {
                          imageUrl = firstImage;
                        } else if (
                          firstImage &&
                          typeof firstImage === "object"
                        ) {
                          if ("url" in firstImage) imageUrl = firstImage.url;
                          else if ("path" in firstImage)
                            imageUrl = firstImage.path;
                          else if ("image_url" in firstImage)
                            imageUrl = firstImage.image_url;
                        }

                        console.log(
                          "Found image in images array:",
                          imageUrl,
                          "Count:",
                          imagesCount
                        );
                      } else if (typeof item.images === "string") {
                        imageUrl = item.images;
                        // Only update imagesCount if we didn't already set it
                        if (imagesCount <= 1) {
                          imagesCount = 1;
                        }
                        console.log("Found images as string:", imageUrl);
                      }
                    }

                    // If no image URL found yet, check sample_image
                    if (
                      !imageUrl &&
                      item.sample_image &&
                      typeof item.sample_image === "string"
                    ) {
                      imageUrl = item.sample_image;
                      console.log("Found sample_image:", imageUrl);
                    }

                    // If we still don't have an image URL, try other possible fields
                    if (!imageUrl) {
                      if (item.image_url) {
                        imageUrl = item.image_url;
                        imagesCount = 1;
                      } else if (item.thumbnail) {
                        imageUrl = item.thumbnail;
                        imagesCount = 1;
                      }
                    }

                    // Make relative URLs absolute
                    if (
                      imageUrl &&
                      !imageUrl.startsWith("http") &&
                      !imageUrl.startsWith("data:") &&
                      !imageUrl.startsWith("blob:")
                    ) {
                      // Remove any leading slashes
                      const cleanUrl = imageUrl.replace(/^\/+/, "");
                      // Use the correct backend URL - change from 3000 to 3000 (your backend port)
                      imageUrl = `http://localhost:3000/${cleanUrl}`;
                      console.log("Converted to absolute URL:", imageUrl);
                    }

                    return (
                      <div className="relative inline-block bg-gray-100 p-1 rounded-md">
                        {imageUrl ? (
                          <img
                            src={imageUrl}
                            alt="Room"
                            className="w-10 h-10 object-cover rounded-md border border-gray-300"
                            onError={(e) => {
                              console.error("Image failed to load:", imageUrl);
                              // Log more details about the error
                              console.log("Image element:", e.currentTarget);
                              // Fallback to a placeholder image
                              e.currentTarget.src =
                                "https://via.placeholder.com/100x100?text=No+Image";
                            }}
                          />
                        ) : (
                          <div className="w-10 h-10 flex items-center justify-center bg-gray-200 rounded-md border border-gray-300">
                            <span className="text-xs text-gray-500">
                              No Image
                            </span>
                          </div>
                        )}
                        {imagesCount > 1 && (
                          <span className="absolute -bottom-1 -right-1 text-xs font-medium text-blue-600 bg-white px-1.5 py-0.5 rounded-full border border-gray-300 shadow-md">
                            +{imagesCount - 1}
                          </span>
                        )}
                      </div>
                    );
                  },
                },
              ]}
              onEdit={(room) => openModal("edit", room)}
              onDelete={(id) => openDeleteConfirmModal(id)}
              customActions={[
                {
                  icon: (
                    <Eye
                      size={16}
                      className="text-blue-600 hover:text-blue-800"
                    />
                  ),
                  tooltip: "View Room",
                  onClick: (room) => openViewModal(room),
                },
              ]}
              startIndex={startingSerialNumber}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              idField="id"
              showCheckboxes={true}
              onSelectedRowsChange={handleSelectedRowsChange}
              onDeleteSelected={openMultiDeleteConfirmModal}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
            />
          </div>

          {totalItems > 0 && (
            <div className="mt-2">
              <Pagination
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                startingSerialNumber={startingSerialNumber}
              />
            </div>
          )}
        </ComponentCard>
      )}

      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Room" : "Add Room"}
        onSubmit={(e) => {
          e.preventDefault();
          console.log("Form submitted");

          try {
            // If editing, include the ID
            const submitData = { ...formData };
            if (selectedRoom?.id) {
              submitData.id = selectedRoom.id;
            }

            // Add debugging
            console.log("Submit data:", submitData);
            console.log("Selected files:", selectedFiles.length);
            console.log("Current multiRoomDetails:", multiRoomDetails);

            // Simple direct validation for critical fields
            let hasErrors = false;
            const directErrors: { [key: string]: string } = {};

            if (!submitData.room_type_id) {
              console.error("Missing room_type_id");
              directErrors.room_type_id = "Room type is required";
              hasErrors = true;
            }

            if (!submitData.description) {
              console.error("Missing description");
              directErrors.description = "Description is required";
              hasErrors = true;
            }

            // For add mode with quantity=1, check room_number
            if (
              modalType === "add" &&
              (submitData.quantity || 0) <= 1 &&
              !submitData.room_number
            ) {
              console.error("Missing room_number");
              directErrors.room_number = "Room number is required";
              hasErrors = true;
            }

            if (hasErrors) {
              setErrors(directErrors);
              return;
            }

            // Call the save handler directly
            console.log("Calling handleSave with data:", submitData);
            handleSave(submitData);
          } catch (error) {
            console.error("Error in form submission:", error);
            toast.error("An error occurred while submitting the form");
          }
        }}
      >
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="grid grid-cols-2 gap-4 col-span-2">
            {/* Room Type */}
            <div>
              <Label>
                Room Type <span className="text-black dark:text-white">*</span>
              </Label>
              <select
                name="room_type_id"
                value={formData.room_type_id || ""}
                onChange={(e) =>
                  setFormData({
                    ...formData,
                    room_type_id: parseInt(e.target.value),
                  })
                }
                className={`w-full p-3 border-2 ${
                  errors.room_type_id ? "border-red-500" : "border-gray-300"
                } rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
              >
                <option value="">Select Room Type</option>
                {roomTypes.map((type) => (
                  <option key={type.id} value={type.id}>
                    {type.name}
                  </option>
                ))}
              </select>
              {errors.room_type_id && (
                <p className="mt-1 text-sm text-red-500">
                  {errors.room_type_id}
                </p>
              )}
            </div>

            {/* Amenities */}
            <div>
              <Label>
                Amenities <span className="text-black dark:text-white">*</span>
              </Label>
              <div className="relative mt-2 amenities-dropdown-container">
                {/* Dropdown trigger */}
                <div
                  className={`p-3 border-2 ${
                    errors.amenities ? "border-red-500" : "border-gray-300"
                  } rounded-md focus:outline-none focus:ring-2 focus:ring-blue-400 dark:bg-gray-700 dark:border-gray-600 dark:text-white cursor-pointer`}
                  onClick={() =>
                    setIsAmenitiesDropdownOpen(!isAmenitiesDropdownOpen)
                  }
                >
                  <div className="flex flex-wrap gap-2">
                    {formData.amenities &&
                    Array.isArray(formData.amenities) &&
                    formData.amenities.length > 0 ? (
                      amenities
                        .filter(
                          (amenity) =>
                            formData.amenities &&
                            Array.isArray(formData.amenities) &&
                            formData.amenities.includes(amenity.id)
                        )
                        .map((amenity) => (
                          <div
                            key={amenity.id}
                            className="bg-blue-100 text-blue-800 px-2 py-1 rounded-md flex items-center"
                          >
                            <span>{amenity.name}</span>
                            <button
                              type="button"
                              onClick={(e) => {
                                e.stopPropagation();
                                const updatedAmenities =
                                  formData.amenities &&
                                  Array.isArray(formData.amenities)
                                    ? formData.amenities.filter(
                                        (id) => id !== amenity.id
                                      )
                                    : [];
                                setFormData({
                                  ...formData,
                                  amenities: updatedAmenities,
                                });
                              }}
                              className="ml-1 text-blue-600 hover:text-blue-800"
                            >
                              <X size={16} />
                            </button>
                          </div>
                        ))
                    ) : (
                      <span className="text-gray-500">Select amenities...</span>
                    )}
                  </div>
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                    {isAmenitiesDropdownOpen ? (
                      <ChevronUp size={20} className="text-gray-500" />
                    ) : (
                      <ChevronDown size={20} className="text-gray-500" />
                    )}
                  </div>
                </div>
                {errors.amenities && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.amenities}
                  </p>
                )}

                {/* Dropdown menu */}
                {isAmenitiesDropdownOpen && (
                  <div
                    className="absolute w-full mt-1 border-2 border-gray-300 rounded-md shadow-lg bg-white z-20 max-h-60 overflow-y-auto"
                    onClick={(e) => e.stopPropagation()}
                  >
                    {amenities.map((amenity) => (
                      <div
                        key={amenity.id}
                        className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${
                          formData.amenities &&
                          Array.isArray(formData.amenities) &&
                          formData.amenities.includes(amenity.id)
                            ? "bg-blue-50"
                            : ""
                        }`}
                        onClick={() => {
                          const currentAmenities =
                            formData.amenities &&
                            Array.isArray(formData.amenities)
                              ? [...formData.amenities]
                              : [];
                          if (currentAmenities.includes(amenity.id)) {
                            // Remove if already selected
                            const updatedAmenities = currentAmenities.filter(
                              (id) => id !== amenity.id
                            );
                            setFormData({
                              ...formData,
                              amenities: updatedAmenities,
                            });
                          } else {
                            // Add if not selected
                            setFormData({
                              ...formData,
                              amenities: [...currentAmenities, amenity.id],
                            });
                          }
                        }}
                      >
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={
                              formData.amenities &&
                              Array.isArray(formData.amenities) &&
                              formData.amenities.includes(amenity.id)
                            }
                            readOnly
                            className="mr-3 h-4 w-4 text-blue-600 rounded focus:ring-2 focus:ring-blue-400 cursor-pointer"
                          />
                          <span>{amenity.name}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Images */}
          <div className="col-span-2">
            <Label>
              Room Images <span className="text-black dark:text-white">*</span>
            </Label>
            <div
              className={`mt-2 ${
                errors.images ? "border border-red-500 p-2 rounded-md" : ""
              }`}
            >
              <input
                type="file"
                ref={fileInputRef}
                onChange={(e) => {
                  if (e.target.files) {
                    const filesArray = Array.from(e.target.files);

                    // Store the actual File objects
                    setSelectedFiles((prevFiles) => [
                      ...prevFiles,
                      ...filesArray,
                    ]);

                    // Create preview URLs with proper structure
                    const newImageUrls = filesArray.map((file) => ({
                      url: URL.createObjectURL(file),
                      // Use a temporary ID for new images
                      id: Math.random() * -1000, // Negative ID to distinguish from server images
                      isNew: true, // Flag to identify newly added images
                    }));

                    // Update form data with new image URLs
                    const currentImages = Array.isArray(formData.images)
                      ? formData.images
                      : [];
                    setFormData({
                      ...formData,
                      images: [...currentImages, ...newImageUrls] as any,
                    });

                    console.log(
                      `Added ${filesArray.length} new files. Total files: ${filesArray.length}`
                    );
                  }
                }}
                accept="image/*"
                multiple
                className="hidden"
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                className="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
              >
                Upload Images
              </button>
              {errors.images && (
                <p className="mt-1 text-sm text-red-500">{errors.images}</p>
              )}

              {/* Image previews */}
              {formData.images && formData.images.length > 0 && (
                <div className="mt-4 grid grid-cols-4 gap-4">
                  {formData.images.map((image, index) => {
                    // Handle image URL based on format
                    let imageUrl = "";
                    let imageId: number | undefined;

                    if (typeof image === "string") {
                      imageUrl = image;
                    } else if (image && typeof image === "object") {
                      if ("url" in image) {
                        // Get the URL directly from the image object
                        imageUrl = (image as { url: string }).url;

                        // If it has an ID, store it
                        if ("id" in image) {
                          imageId = (image as { id: number }).id;
                        }

                        // If it's a relative URL, make it absolute
                        if (
                          !imageUrl.startsWith("http") &&
                          !imageUrl.startsWith("data:") &&
                          !imageUrl.startsWith("blob:")
                        ) {
                          // For development environment - direct URL to backend
                          // Remove any leading slashes
                          const cleanUrl = imageUrl.replace(/^\/+/, "");
                          imageUrl = `http://localhost:3000/${cleanUrl}`;
                        }
                      }
                    }

                    console.log("Preview image URL:", imageUrl, "ID:", imageId);

                    return (
                      <div key={index} className="relative group">
                        <img
                          src={imageUrl}
                          alt={`Room preview ${index + 1}`}
                          className="w-full h-24 object-cover rounded-md border border-gray-300"
                          onError={(e) => {
                            console.log(
                              "Preview image failed to load:",
                              imageUrl
                            );
                            // Fallback to a placeholder image
                            e.currentTarget.src =
                              "https://via.placeholder.com/240x120?text=No+Image";
                          }}
                        />
                        <button
                          type="button"
                          onClick={() => {
                            const currentImages = Array.isArray(formData.images)
                              ? [...formData.images]
                              : [];

                            // If the image has an ID, add it to removedImageIds if it's a positive ID (from server)
                            if (imageId && imageId > 0) {
                              const currentRemovedIds =
                                formData.removedImageIds || [];
                              const filteredImages = currentImages.filter(
                                (_, i) => i !== index
                              ) as any;
                              setFormData({
                                ...formData,
                                images: filteredImages,
                                removedImageIds: [
                                  ...currentRemovedIds,
                                  imageId,
                                ],
                              });
                              console.log(
                                `Added image ID ${imageId} to removedImageIds`
                              );
                            } else {
                              // For new images or images without ID, just remove from array
                              // Also release the object URL to prevent memory leaks
                              if (
                                typeof image === "object" &&
                                "url" in image &&
                                image.url.startsWith("blob:")
                              ) {
                                URL.revokeObjectURL(image.url);
                              }

                              // Also remove from selectedFiles if it's a new image
                              if (
                                typeof image === "object" &&
                                "isNew" in image &&
                                image.isNew
                              ) {
                                // Find the corresponding file in selectedFiles and remove it
                                // This is approximate since we don't have a direct link between the file and preview
                                if (selectedFiles.length > index) {
                                  const newSelectedFiles = [...selectedFiles];
                                  newSelectedFiles.splice(index, 1);
                                  setSelectedFiles(newSelectedFiles);
                                }
                              }

                              currentImages.splice(index, 1);
                              setFormData({
                                ...formData,
                                images: currentImages as any,
                              });
                            }
                          }}
                          className="absolute top-1 right-1 bg-red-500 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <X size={16} />
                        </button>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <div className="col-span-2">
            <Label>
              Description <span className="text-black dark:text-white">*</span>
            </Label>
            <textarea
              name="description"
              value={formData.description || ""}
              onChange={(e) =>
                setFormData({ ...formData, description: e.target.value })
              }
              className={`w-full p-3 border-2 ${
                errors.description ? "border-red-500" : "border-gray-300"
              } rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
              rows={3}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-500">{errors.description}</p>
            )}
          </div>

          {/* Room Details Section - At the bottom of the form with distinct border */}
          <div className="col-span-2 mt-4 pt-4 border-t border-gray-200">
            <div className="border-2 border-gray-200 rounded-md p-4 bg-gray-50">
              <div className="grid grid-cols-4 gap-4">
                {/* Quantity - For both Add and Edit modes */}
                <div>
                  <Label>
                    Quantity{" "}
                    <span className="text-black dark:text-white">*</span>
                  </Label>
                  <Input
                    type="number"
                    name="quantity"
                    value={formData.quantity?.toString() || "1"}
                    onChange={(e) => {
                      const value = parseInt(e.target.value) || 1;
                      setFormData({ ...formData, quantity: value });

                      // Initialize multiRoomDetails array with the correct number of items
                      if (value > 1) {
                        // Generate sequential room numbers starting from 101
                        const startRoomNumber = 101;

                        const newDetails = Array(value)
                          .fill(0)
                          .map((_, index) => ({
                            room_number: `${startRoomNumber + index}`,
                            floor_number: "1", // Default floor number
                            status: "available" as
                              | "available"
                              | "occupied"
                              | "maintenance", // Default status
                          }));

                        console.log("Generated multiRoomDetails:", newDetails);
                        setMultiRoomDetails(newDetails);
                      } else {
                        setMultiRoomDetails([]);
                      }
                    }}
                    min="1"
                    // Allow editing in both modes
                    error={!!errors.quantity}
                    hint={errors.quantity || ""}
                  />
                </div>

                {/* Room Details */}
                {(modalType === "edit" || (formData.quantity || 0) <= 1) && (
                  <>
                    {/* Room Number */}
                    <div>
                      <Label>
                        Room Number{" "}
                        <span className="text-black dark:text-white">*</span>
                      </Label>
                      <Input
                        type="text"
                        name="room_number"
                        value={formData.room_number}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            room_number: e.target.value,
                          })
                        }
                        error={!!errors.room_number}
                        hint={errors.room_number || ""}
                      />
                    </div>

                    {/* Floor Number */}
                    <div>
                      <Label>
                        Floor Number{" "}
                        <span className="text-black dark:text-white">*</span>
                      </Label>
                      <Input
                        type="text"
                        name="floor_number"
                        value={formData.floor_number}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            floor_number: e.target.value,
                          })
                        }
                        error={!!errors.floor_number}
                        hint={errors.floor_number || ""}
                      />
                    </div>

                    {/* Status */}
                    <div>
                      <Label>
                        Status{" "}
                        <span className="text-black dark:text-white">*</span>
                      </Label>
                      <select
                        name="status"
                        value={formData.status || "available"}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            status: e.target.value as
                              | "available"
                              | "occupied"
                              | "maintenance",
                          })
                        }
                        className="w-full border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        style={{ height: "42px", padding: "0 12px" }} // Match the height of Input component
                      >
                        <option value="available">Available</option>
                        <option value="occupied">Occupied</option>
                        <option value="maintenance">Maintenance</option>
                      </select>
                    </div>
                  </>
                )}
              </div>

              {/* Multiple Room Details (when quantity > 1) */}
              {(formData.quantity || 0) > 1 && (
                <div className="mt-4">
                  {(errors.multiRoomNumbers || errors.multiFloorNumbers) && (
                    <div className="text-red-500 text-sm mb-2">
                      {errors.multiRoomNumbers && (
                        <p>{errors.multiRoomNumbers}</p>
                      )}
                      {errors.multiFloorNumbers && (
                        <p>{errors.multiFloorNumbers}</p>
                      )}
                    </div>
                  )}

                  {/* Quick Actions */}
                  <div className="mb-4 grid grid-cols-3 gap-3">
                    {/* Generate Room Numbers */}
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        placeholder="Start #"
                        defaultValue="101"
                        className="w-20 p-1 border-2 border-gray-300 rounded-md"
                        id="startRoomNumber"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const startInput = document.getElementById(
                            "startRoomNumber"
                          ) as HTMLInputElement;
                          if (startInput && startInput.value) {
                            const startValue = parseInt(startInput.value);
                            if (!isNaN(startValue)) {
                              const newDetails = multiRoomDetails.map(
                                (room, index) => ({
                                  ...room,
                                  room_number: (startValue + index).toString(),
                                })
                              );
                              setMultiRoomDetails(newDetails);
                            }
                          }
                        }}
                        className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                      >
                        Generate Numbers
                      </button>
                    </div>

                    {/* Set All Floor Numbers */}
                    <div className="flex items-center gap-2">
                      <input
                        type="text"
                        placeholder="Floor #"
                        className="w-20 p-1 border-2 border-gray-300 rounded-md"
                        id="bulkFloorNumber"
                        defaultValue="1"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const floorInput = document.getElementById(
                            "bulkFloorNumber"
                          ) as HTMLInputElement;
                          if (floorInput && floorInput.value) {
                            const floorValue = floorInput.value;
                            const newDetails = multiRoomDetails.map((room) => ({
                              ...room,
                              floor_number: floorValue,
                            }));
                            setMultiRoomDetails(newDetails);
                          }
                        }}
                        className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                      >
                        Set All Floors
                      </button>
                    </div>

                    {/* Set All Status */}
                    <div className="flex items-center gap-2">
                      <select
                        id="bulkStatus"
                        className="w-28 p-1 border-2 border-gray-300 rounded-md"
                      >
                        <option value="available">Available</option>
                        <option value="occupied">Occupied</option>
                        <option value="maintenance">Maintenance</option>
                      </select>
                      <button
                        type="button"
                        onClick={() => {
                          const statusSelect = document.getElementById(
                            "bulkStatus"
                          ) as HTMLSelectElement;
                          if (statusSelect && statusSelect.value) {
                            const statusValue = statusSelect.value as
                              | "available"
                              | "occupied"
                              | "maintenance";
                            const newDetails = multiRoomDetails.map((room) => ({
                              ...room,
                              status: statusValue,
                            }));
                            setMultiRoomDetails(newDetails);
                          }
                        }}
                        className="px-2 py-1 bg-blue-500 text-white text-xs rounded hover:bg-blue-600"
                      >
                        Set All Status
                      </button>
                    </div>
                  </div>

                  {/* Room Details Grid */}
                  <div className="grid grid-cols-1 gap-4 max-h-60 overflow-y-auto pr-2">
                    {multiRoomDetails.map((room, index) => (
                      <div
                        key={index}
                        className="p-3 border-2 border-gray-200 rounded-md"
                      >
                        <div className="mb-2 text-sm text-gray-500">
                          Room #{index + 1}
                        </div>
                        <div className="grid grid-cols-3 gap-3">
                          {/* Room Number */}
                          <div>
                            <Label className="text-sm">
                              Room Number{" "}
                              <span className="text-black dark:text-white">
                                *
                              </span>
                            </Label>
                            <Input
                              type="text"
                              value={room.room_number}
                              onChange={(e) => {
                                const newDetails = [...multiRoomDetails];
                                newDetails[index].room_number = e.target.value;
                                setMultiRoomDetails(newDetails);
                              }}
                              error={
                                !!errors.multiRoomNumbers &&
                                (!room.room_number ||
                                  room.room_number.trim() === "")
                              }
                            />
                          </div>

                          {/* Floor Number */}
                          <div>
                            <Label className="text-sm">
                              Floor Number{" "}
                              <span className="text-black dark:text-white">
                                *
                              </span>
                            </Label>
                            <Input
                              type="text"
                              value={room.floor_number}
                              onChange={(e) => {
                                const newDetails = [...multiRoomDetails];
                                newDetails[index].floor_number = e.target.value;
                                setMultiRoomDetails(newDetails);
                              }}
                              error={
                                !!errors.multiFloorNumbers &&
                                (!room.floor_number ||
                                  room.floor_number.trim() === "")
                              }
                            />
                          </div>

                          {/* Status */}
                          <div>
                            <Label className="text-sm">
                              Status{" "}
                              <span className="text-black dark:text-white">
                                *
                              </span>
                            </Label>
                            <select
                              value={room.status}
                              onChange={(e) => {
                                const newDetails = [...multiRoomDetails];
                                newDetails[index].status = e.target.value as
                                  | "available"
                                  | "occupied"
                                  | "maintenance";
                                setMultiRoomDetails(newDetails);
                              }}
                              className="w-full border-2 border-gray-300 rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                              style={{ height: "42px", padding: "0 12px" }} // Match the height of Input component
                            >
                              <option value="available">Available</option>
                              <option value="occupied">Occupied</option>
                              <option value="maintenance">Maintenance</option>
                            </select>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </GenericAddEditModal>

      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        UserName={
          deletingId
            ? `Room No: ${
                rooms.find((room) => room.id === deletingId)?.room_number || ""
              }`
            : ""
        }
        selectedItems={
          selectedRows.length > 0
            ? selectedRows
                .map(
                  (id) =>
                    `Room No: ${
                      rooms.find((room) => room.id === id)?.room_number ||
                      `#${id}`
                    }`
                )
                .filter(Boolean)
            : undefined
        }
      />

      {/* View Room Modal */}
      <GenericViewModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        title={`Room Details: ${
          roomTypeDetails?.room_type_name || selectedRoom?.room_type || ""
        }`}
        loading={viewLoading}
      >
        {roomTypeDetails ? (
          <>
            {/* Room Type Images */}
            {roomTypeDetails.images && roomTypeDetails.images.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Room Images
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {roomTypeDetails.images.map((image: any, index: number) => {
                    let imageUrl = "";
                    if (typeof image === "string") {
                      imageUrl = image;
                    } else if (
                      image &&
                      typeof image === "object" &&
                      "url" in image
                    ) {
                      imageUrl = image.url;
                    } else if (
                      image &&
                      typeof image === "object" &&
                      "image_url" in image
                    ) {
                      imageUrl = image.image_url;
                    }

                    // If it's a relative URL, make it absolute
                    if (
                      imageUrl &&
                      !imageUrl.startsWith("http") &&
                      !imageUrl.startsWith("data:")
                    ) {
                      const cleanUrl = imageUrl.replace(/^\/+/, "");
                      imageUrl = `http://localhost:3000/${cleanUrl}`;
                    }

                    return (
                      <div key={index} className="relative">
                        <img
                          src={imageUrl}
                          alt={`Room ${index + 1}`}
                          className="w-full h-40 object-cover rounded-md shadow-md"
                          onError={(e) => {
                            e.currentTarget.src =
                              "https://via.placeholder.com/300x200?text=No+Image";
                          }}
                        />
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Room Type Information */}
            <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 mb-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Room Type
                  </h4>
                  <p className="mt-1 text-base text-gray-900 font-semibold">
                    {roomTypeDetails.room_type_name || "-"}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium text-gray-500">
                    Description
                  </h4>
                  <p className="mt-1 text-base text-gray-900">
                    {roomTypeDetails.description || "-"}
                  </p>
                </div>
              </div>
            </div>

            {/* Amenities */}
            {roomTypeDetails.amenities &&
              roomTypeDetails.amenities.length > 0 && (
                <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 mb-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Amenities
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {roomTypeDetails.amenities.map(
                      (amenity: string, index: number) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {amenity}
                        </span>
                      )
                    )}
                  </div>
                </div>
              )}

            {/* Individual Rooms */}
            {roomTypeDetails.rooms && roomTypeDetails.rooms.length > 0 && (
              <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Available Rooms
                </h3>
                <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 rounded-lg">
                  <table className="min-w-full divide-y divide-gray-300">
                    <thead className="bg-gray-50">
                      <tr>
                        <th
                          scope="col"
                          className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900"
                        >
                          Room Number
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Floor
                        </th>
                        <th
                          scope="col"
                          className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900"
                        >
                          Status
                        </th>
                      </tr>
                    </thead>
                    <tbody className="divide-y divide-gray-200 bg-white">
                      {roomTypeDetails.rooms.map((room: any, index: number) => (
                        <tr
                          key={index}
                          className={
                            index % 2 === 0 ? "bg-white" : "bg-gray-50"
                          }
                        >
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900">
                            {room.room_number}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {room.floor_number}
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            <span
                              className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-4 ${
                                room.status === "available"
                                  ? "bg-green-100 text-green-800"
                                  : room.status === "occupied"
                                  ? "bg-blue-100 text-blue-800"
                                  : room.status === "maintenance"
                                  ? "bg-red-100 text-red-800"
                                  : "bg-gray-100 text-gray-800"
                              }`}
                            >
                              {room.status === "available"
                                ? "Available"
                                : room.status === "occupied"
                                ? "Occupied"
                                : room.status === "maintenance"
                                ? "Maintenance"
                                : "Unknown"}
                            </span>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </>
        ) : (
          selectedRoom && (
            <>
              {/* Room Images */}
              {selectedRoom.images && selectedRoom.images.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Room Images
                  </h3>
                  <div className="grid grid-cols-2 gap-4">
                    {selectedRoom.images.map((image: any, index: number) => {
                      let imageUrl = "";
                      if (typeof image === "string") {
                        imageUrl = image;
                      } else if (
                        image &&
                        typeof image === "object" &&
                        "url" in image
                      ) {
                        imageUrl = image.url;
                      } else if (
                        image &&
                        typeof image === "object" &&
                        "image_url" in image
                      ) {
                        imageUrl = image.image_url;
                      }

                      // If it's a relative URL, make it absolute
                      if (
                        imageUrl &&
                        !imageUrl.startsWith("http") &&
                        !imageUrl.startsWith("data:")
                      ) {
                        const cleanUrl = imageUrl.replace(/^\/+/, "");
                        imageUrl = `http://localhost:3000/${cleanUrl}`;
                      }

                      return (
                        <div key={index} className="relative">
                          <img
                            src={imageUrl}
                            alt={`Room ${index + 1}`}
                            className="w-full h-40 object-cover rounded-md shadow-md"
                            onError={(e) => {
                              e.currentTarget.src =
                                "https://via.placeholder.com/300x200?text=No+Image";
                            }}
                          />
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}

              {/* Room Details */}
              <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 mb-4">
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  Room Details
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500">
                      Room Number
                    </h4>
                    <p className="mt-1 text-base text-gray-900">
                      {selectedRoom.room_number || "-"}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500">
                      Room Type
                    </h4>
                    <p className="mt-1 text-base text-gray-900">
                      {selectedRoom.room_type || "-"}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500">Floor</h4>
                    <p className="mt-1 text-base text-gray-900">
                      {selectedRoom.floor_number || "-"}
                    </p>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500">
                      Status
                    </h4>
                    <p className="mt-1">
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold leading-4 ${
                          selectedRoom.status === "available"
                            ? "bg-green-100 text-green-800"
                            : selectedRoom.status === "occupied"
                            ? "bg-blue-100 text-blue-800"
                            : selectedRoom.status === "maintenance"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {selectedRoom.status === "available"
                          ? "Available"
                          : selectedRoom.status === "occupied"
                          ? "Occupied"
                          : selectedRoom.status === "maintenance"
                          ? "Maintenance"
                          : "Unknown"}
                      </span>
                    </p>
                  </div>
                </div>
              </div>

              {/* Amenities */}
              <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200 mb-4">
                <h3 className="text-lg font-medium text-gray-500">Amenities</h3>
                <div className="flex flex-wrap gap-2 mt-2">
                  {typeof selectedRoom.amenities === "string" ? (
                    selectedRoom.amenities
                      .split(",")
                      .map((amenity: string, index: number) => (
                        <span
                          key={index}
                          className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                        >
                          {amenity.trim()}
                        </span>
                      ))
                  ) : Array.isArray(selectedRoom.amenities) ? (
                    selectedRoom.amenities.length > 0 ? (
                      selectedRoom.amenities.map(
                        (amenity: string, index: number) => (
                          <span
                            key={index}
                            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium"
                          >
                            {amenity}
                          </span>
                        )
                      )
                    ) : (
                      <span className="text-gray-500">
                        No amenities available
                      </span>
                    )
                  ) : (
                    <span className="text-gray-500">
                      No amenities available
                    </span>
                  )}
                </div>
              </div>

              {/* Description */}
              <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
                <h3 className="text-lg font-medium text-gray-500">
                  Description
                </h3>
                <p className="mt-1 text-base text-gray-900">
                  {selectedRoom.description || "-"}
                </p>
              </div>
            </>
          )
        )}
      </GenericViewModal>
    </>
  );
};

export default RoomManagement;
