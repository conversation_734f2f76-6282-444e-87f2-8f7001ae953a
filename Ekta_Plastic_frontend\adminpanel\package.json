{"name": "tailadmin-react", "private": true, "version": "2.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fullcalendar/core": "^6.1.15", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.15", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-jvectormap/core": "^1.0.4", "@react-jvectormap/world": "^1.1.2", "@tailwindcss/forms": "^0.5.10", "apexcharts": "^4.1.0", "axios": "^1.8.3", "classnames": "^2.5.1", "clsx": "^2.1.1", "flatpickr": "^4.6.13", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.485.0", "react": "^18.3.1", "react-apexcharts": "^1.7.0", "react-datepicker": "^8.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-flatpickr": "^3.10.13", "react-helmet-async": "^2.0.5", "react-icons": "^5.5.0", "react-router-dom": "^7.1.5", "react-router-dom-dom": "^7.4.1", "react-toastify": "^11.0.5", "simplebar-react": "^3.3.0", "swiper": "^11.2.3", "tailwind-merge": "^3.0.1", "uninstall": "^0.0.0"}, "devDependencies": {"@eslint/js": "^9.19.0", "@tailwindcss/postcss": "^4.0.8", "@types/lodash": "^4.17.17", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@types/react-flatpickr": "^3.8.11", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.19.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.6", "typescript": "~5.7.2", "typescript-eslint": "^8.22.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}}