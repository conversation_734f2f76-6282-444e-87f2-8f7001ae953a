import { useCallback, useEffect, useRef, useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { jwtDecode } from "jwt-decode"; // JWT decode ke liye
import axios from "axios"; // API call ke liye
import { ChevronDownIcon, GridIcon } from "../icons";
import { Settings, Users } from "lucide-react";
import { useSidebar } from "../context/SidebarContext";
import AppLogo from "../components/common/AppLogo";

type NavItem = {
  name: string;
  icon: React.ReactNode;
  path?: string;
  subItems?: { name: string; path: string; pro?: boolean; new?: boolean }[];
};

// Static nav items
const allNavItems: Record<string, NavItem[]> = {
  Admin: [
    {
      icon: <GridIcon />,
      name: "Dashboard",
      path: "/admindashboard",
    },

    {
      icon: <Settings size={20} />,
      name: "Hotel Settings",
      subItems: [
        { name: "Amenities", path: "/amenities", pro: false },
        // { name: "Room Types", path: "/room-types", pro: false },
        // { name: "Rooms", path: "/rooms", pro: false },
      ],
    },
    {
      icon: <Settings size={20} />,
      name: "Product Management",
      subItems: [
        { name: "Product Categories", path: "/product-categories", pro: false },
        { name: "Products", path: "/products", pro: false },
        { name: "Product Images", path: "/product-images", pro: false },
      ],
    },
    {
      icon: <Users size={20} />,
      name: "User Management",
      path: "/hotelmaster",
    },
  ],
  Manager: [
    {
      icon: <GridIcon />,
      name: "Dashboard",
      path: "/manager",
    },
    {
      icon: <Settings size={20} />,
      name: "Hotel Settings",
      subItems: [
        { name: "Room Types", path: "/room-types", pro: false },
        { name: "Rooms", path: "/rooms", pro: false },
        { name: "Room Add-Ons", path: "/room-addons", pro: false },
        { name: "Rooms Rates", path: "/room-rates", pro: false },
        { name: "Bookings", path: "/bookings", pro: false },
      ],
    },
    {
      icon: <Settings size={20} />,
      name: "Product Management",
      subItems: [
        { name: "Product Categories", path: "/product-categories", pro: false },
        { name: "Products", path: "/products", pro: false },
        { name: "Product Images", path: "/product-images", pro: false },
      ],
    },
  ],
  Customer: [
    {
      icon: <GridIcon />,
      name: "CustomerDashboard",
      path: "/customer",
    },
  ],
  "Hotel Master": [
    {
      icon: <GridIcon />,
      name: "Dashboard",
      path: "/",
    },
    {
      icon: <Settings size={20} />,
      name: "Hotel Settings",
      subItems: [
        { name: "Amenities", path: "/amenities", pro: false },
        { name: "Room Types", path: "/room-types", pro: false },
        { name: "Rooms", path: "/rooms", pro: false },
        { name: "Room Add-Ons", path: "/room-addons", pro: false },
        { name: "Rooms Rates", path: "/room-rates", pro: false },
        { name: "Bookings", path: "/bookings", pro: false },
        { name: "Customers", path: "/customer-Manager", pro: false },
      ],
    },
    {
      icon: <Settings size={20} />,
      name: "Product Management",
      subItems: [
        { name: "Product Categories", path: "/product-categories", pro: false },
        { name: "Products", path: "/products", pro: false },
        { name: "Product Images", path: "/product-images", pro: false },
      ],
    },
    {
      icon: <Users size={20} />,
      name: "User Management",
      path: "/hotelmaster",
    },
  ],
};

const AppSidebar: React.FC = () => {
  const { isExpanded, isMobileOpen, isHovered, setIsHovered } = useSidebar();
  const location = useLocation();

  const [openSubmenu, setOpenSubmenu] = useState<number | null>(null);
  const [subMenuHeight, setSubMenuHeight] = useState<Record<number, number>>(
    {}
  );
  const subMenuRefs = useRef<Record<number, HTMLDivElement | null>>({});
  // Store the current user's role name
  const [, setRoleName] = useState<string | null>(null);
  const [navItems, setNavItems] = useState<NavItem[]>([]);

  // Token decode and API call
  useEffect(() => {
    const fetchRoleName = async () => {
      try {
        const token = localStorage.getItem("token");
        if (!token) return;
        console.log("token", token);

        // Decode JWT
        const decoded: { role_id?: number } = jwtDecode(token);
        console.log("Decoded Token:", decoded);
        if (!decoded.role_id) return;

        // API Call for role_name
        const response = await axios.get(
          `http://localhost:3000/api/users/getrolename?role_id=${decoded.role_id}`
        );

        if (response.data.role_name) {
          setRoleName(response.data.role_name);
          console.log("Setting Role Name:", response.data.role_name);
          setNavItems(allNavItems[response.data.role_name] || []);
          console.log(
            "Setting Nav Items:",
            allNavItems[response.data.role_name]
          );
        }
      } catch (error) {
        console.error("Error fetching role_name:", error);
      }
    };

    fetchRoleName();
  }, []);

  const isActive = useCallback(
    (path: string) => location.pathname === path,
    [location.pathname]
  );

  useEffect(() => {
    let submenuMatched = false;
    navItems.forEach((nav, index) => {
      if (nav.subItems) {
        nav.subItems.forEach((subItem) => {
          if (isActive(subItem.path)) {
            setOpenSubmenu(index);
            submenuMatched = true;
          }
        });
      }
    });

    if (!submenuMatched) {
      setOpenSubmenu(null);
    }
  }, [location, isActive, navItems]);

  useEffect(() => {
    if (openSubmenu !== null) {
      if (subMenuRefs.current[openSubmenu]) {
        setSubMenuHeight((prevHeights) => ({
          ...prevHeights,
          [openSubmenu]: subMenuRefs.current[openSubmenu]?.scrollHeight || 0,
        }));
      }
    }
  }, [openSubmenu]);

  const handleSubmenuToggle = (index: number) => {
    setOpenSubmenu((prevIndex) => (prevIndex === index ? null : index));
  };

  return (
    <aside
      className={`fixed mt-16 flex flex-col lg:mt-0 top-0 px-5 left-0 bg-white dark:bg-gray-900 text-gray-900 h-screen transition-all duration-300 ease-in-out z-50 border-r-2 border-gray-300 dark:border-gray-700 shadow-md
      ${
        isExpanded || isMobileOpen
          ? "w-[300px]"
          : isHovered
          ? "w-[300px]"
          : "w-[90px]"
      }
      ${isMobileOpen ? "translate-x-0" : "-translate-x-full"}
      lg:translate-x-0`}
      onMouseEnter={() => !isExpanded && setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div
        className={`py-8 flex px-6 ${
          !isExpanded && !isHovered ? "lg:justify-center" : "justify-start"
        }`}
      >
        <Link to="/">
          {isExpanded || isHovered || isMobileOpen ? (
            <AppLogo size="lg" textColor="text-gray-100" />
          ) : (
            <AppLogo showText={false} size="sm" iconColor="bg-indigo-600" />
          )}
        </Link>
      </div>

      <div className="flex flex-col overflow-y-auto duration-300 ease-linear no-scrollbar">
        <nav className="mb-6">
          <ul className="flex flex-col gap-4">
            {navItems.map((nav, index) => (
              <li key={nav.name}>
                {nav.subItems ? (
                  <>
                    <button
                      onClick={() => handleSubmenuToggle(index)}
                      className={`menu-item group ${
                        openSubmenu === index
                          ? "menu-item-active"
                          : "menu-item-inactive"
                      } cursor-pointer ${
                        !isExpanded && !isHovered
                          ? "lg:justify-center"
                          : "lg:justify-start"
                      }`}
                    >
                      <span
                        className={`menu-item-icon-size ${
                          openSubmenu === index
                            ? "menu-item-icon-active"
                            : "menu-item-icon-inactive"
                        }`}
                      >
                        {nav.icon}
                      </span>
                      {(isExpanded || isHovered || isMobileOpen) && (
                        <span className="menu-item-text">{nav.name}</span>
                      )}
                      {(isExpanded || isHovered || isMobileOpen) && (
                        <ChevronDownIcon
                          className={`ml-auto w-5 h-5 transition-transform duration-200 ${
                            openSubmenu === index
                              ? "rotate-180 text-brand-500"
                              : ""
                          }`}
                        />
                      )}
                    </button>

                    {/* Submenu Items */}
                    {(isExpanded || isHovered || isMobileOpen) && (
                      <div
                        ref={(el) => {
                          if (el) subMenuRefs.current[index] = el;
                        }}
                        className="overflow-hidden transition-all duration-300 ease-in-out"
                        style={{
                          maxHeight:
                            openSubmenu === index
                              ? subMenuHeight[index] || 1000
                              : 0,
                        }}
                      >
                        <ul className="pl-10 mt-2 space-y-2">
                          {nav.subItems?.map((subItem) => (
                            <li key={subItem.name}>
                              <Link
                                to={subItem.path}
                                className={`flex items-center py-2 text-sm font-medium rounded-lg ${
                                  isActive(subItem.path)
                                    ? "text-brand-500 bg-brand-50 dark:bg-gray-800"
                                    : "text-gray-600 hover:text-brand-500 dark:text-gray-400 dark:hover:text-white"
                                }`}
                              >
                                <span className="w-2 h-2 mr-3 rounded-full bg-current"></span>
                                <span>{subItem.name}</span>
                                {subItem.pro && (
                                  <span className="ml-auto px-1.5 py-0.5 text-xs font-medium bg-brand-100 text-brand-500 rounded">
                                    PRO
                                  </span>
                                )}
                                {subItem.new && (
                                  <span className="ml-auto px-1.5 py-0.5 text-xs font-medium bg-success-100 text-success-500 rounded">
                                    NEW
                                  </span>
                                )}
                              </Link>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </>
                ) : (
                  nav.path && (
                    <Link
                      to={nav.path}
                      className={`menu-item group ${
                        isActive(nav.path)
                          ? "menu-item-active"
                          : "menu-item-inactive"
                      }`}
                    >
                      <span
                        className={`menu-item-icon-size ${
                          isActive(nav.path)
                            ? "menu-item-icon-active"
                            : "menu-item-icon-inactive"
                        }`}
                      >
                        {nav.icon}
                      </span>
                      {(isExpanded || isHovered || isMobileOpen) && (
                        <span className="menu-item-text">{nav.name}</span>
                      )}
                    </Link>
                  )
                )}
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </aside>
  );
};

export default AppSidebar;
