import React from "react";
import { Modal } from "../ui/modal";

interface ViewGenericProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const ViewGeneric: React.FC<ViewGenericProps> = ({
  isOpen,
  onClose,
  title,
  children
}) => {
  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-[600px] p-5">
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white">
          {title}
        </h2>
      </div>

      <div className="max-h-[70vh] overflow-y-auto pr-2">
        {children}
      </div>

      <div className="flex items-center gap-3 mt-6 justify-end">
        <button
          type="button"
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
      </div>
    </Modal>
  );
};

export default ViewGeneric;
