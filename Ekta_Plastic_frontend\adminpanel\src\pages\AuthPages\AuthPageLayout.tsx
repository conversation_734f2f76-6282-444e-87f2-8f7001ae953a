import React from "react";
import { Link, useLocation } from "react-router-dom";
import ThemeTogglerTwo from "../../components/common/ThemeTogglerTwo";
import appConfig from "../../config/appConfig";
import AppLogo from "../../components/common/AppLogo";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const location = useLocation();
  const isSignIn = location.pathname === "/signin";

  return (
    <div className="relative p-6 bg-white z-1 dark:bg-gray-900 sm:p-0">
      <div className="relative flex flex-col justify-center w-full h-screen lg:flex-row dark:bg-gray-900 sm:p-0">
        {children}
        <div className="items-center hidden w-full h-full lg:w-1/2 bg-blue-900 dark:bg-blue-900/90 lg:grid">
          <div className="relative flex items-center justify-center z-1">
            <div className="absolute inset-0 opacity-10 bg-[url('data:image/svg+xml,%3Csvg width=\'60\' height=\'60\' viewBox=\'0 0 60 60\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'none\' fill-rule=\'evenodd\'%3E%3Cg fill=\'%23ffffff\' fill-opacity=\'0.1\'%3E%3Cpath d=\'M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z\'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')]"></div>
            <div className="flex flex-col items-center justify-center w-full max-w-md px-6 mx-auto text-center">
              <Link to="/" className="block mb-6">
                <AppLogo size="lg" className="text-white" textColor="text-white" />
              </Link>

              <div className="mb-8 w-full flex justify-center">
                <img
                  src={isSignIn ? "/images/hotel/holiday-mytrip-login.svg" : "/images/hotel/holiday-mytrip-signup.svg"}
                  alt={`${appConfig.appName} Illustration`}
                  className="w-auto h-auto max-h-60 max-w-[200px]"
                />
              </div>

              <h2 className="text-2xl font-bold text-white mb-4">
                {isSignIn ? "Welcome Back!" : `Join Our ${appConfig.appName} System`}
              </h2>

              <p className="text-center text-blue-100 mb-6">
                {isSignIn
                  ? `Sign in to access your ${appConfig.appName} dashboard and tools.`
                  : `Register to start managing your ${appConfig.appName} properties efficiently.`}
              </p>

              <div className="flex space-x-4">
                <div className="bg-white/10 p-3 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                    <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                </div>
                <div className="bg-white/10 p-3 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                    <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path>
                    <path d="M13 13h4"></path>
                    <path d="M13 17h4"></path>
                  </svg>
                </div>
                <div className="bg-white/10 p-3 rounded-lg">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-white">
                    <path d="M16 2H8a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2Z"></path>
                    <path d="M9 22v-4h6v4"></path>
                    <path d="M8 2v20"></path>
                    <path d="M16 2v20"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="fixed z-50 hidden bottom-6 right-6 sm:block">
          <ThemeTogglerTwo />
        </div>
      </div>
    </div>
  );
}
