import React from "react";
import ViewGeneric from "../common/ViewGeneric";

interface GenericViewProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  loading?: boolean;
  children: React.ReactNode; // Use children instead of content
}

const GenericViewModal: React.FC<GenericViewProps> = ({ 
  isOpen, 
  onClose, 
  title, 
  loading = false,
  children 
}) => {
  if (!isOpen) return null;

  return (
    <ViewGeneric
      isOpen={isOpen}
      onClose={onClose}
      title={title}
    >
      {loading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <div className="space-y-6">
          {children}
        </div>
      )}
    </ViewGeneric>
  );
};

export default GenericViewModal;