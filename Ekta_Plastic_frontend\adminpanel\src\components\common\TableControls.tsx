import React from "react";
import GenericSearch from "./GenericSearch";
import GenericAddButton from "./GenericAddButton";

interface TableControlsProps {
  search: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  itemsPerPage: number;
  // totalItems: number;
  onItemsPerPageChange: (value: number) => void;
  onAddClick: () => void;
  addButtonLabel?: string;
  perPageOptions?: number[];
  searchPlaceholder?: string;
  className?: string;
}

const TableControls: React.FC<TableControlsProps> = ({
  search,
  onSearchChange,
  itemsPerPage,
  onItemsPerPageChange,
  onAddClick,
  addButtonLabel = "Add",
  perPageOptions,
  searchPlaceholder,
  className = "",
}) => {
  return (
    <div className={`flex justify-between items-center mb-3 ${className}`}>
      <div className="flex items-center space-x-2">
        <span className="text-sm text-gray-600 dark:text-gray-300">Show</span>
        <select
          value={itemsPerPage}
          onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          className="px-3 py-1.5 border rounded-md bg-white text-sm text-gray-800 dark:bg-gray-800 dark:text-white dark:border-gray-600"
        >
          {(perPageOptions || [10, 15, 25, 50]).map((option) => (
            <option key={option} value={option}>
              {option}
            </option>
          ))}
        </select>
        <span className="text-sm text-gray-600 dark:text-gray-300">
          entries
        </span>
      </div>

      <div className="flex items-center space-x-2">
        <GenericSearch
          value={search}
          onChange={onSearchChange}
          placeholder={searchPlaceholder}
        />
        <GenericAddButton onClick={onAddClick} label={addButtonLabel} />
      </div>
    </div>
  );
};

export default TableControls;
