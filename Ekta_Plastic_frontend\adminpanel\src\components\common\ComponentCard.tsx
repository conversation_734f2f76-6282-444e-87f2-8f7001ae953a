interface ComponentCardProps {
  title: string;
  children: React.ReactNode;
  className?: string; // Additional custom classes for styling
  desc?: string; // Description text
  scrollable?: boolean; // Whether the content should be scrollable
  maxHeight?: string; // Maximum height for scrollable content
}

const ComponentCard: React.FC<ComponentCardProps> = ({
  title,
  children,
  className = "",
  desc = "",
  scrollable = false,
  maxHeight = "calc(100vh - 200px)", // Default max height
}) => {
  return (
    <div
      className={`rounded-2xl border border-gray-200 bg-white dark:border-gray-800 dark:bg-white/[0.03] overflow-hidden ${
        scrollable ? "flex flex-col h-full" : ""
      } ${className}`}
    >
      {/* Card Header */}
      <div className="px-6 py-2">
        <h3 className="text-base font-medium text-gray-800 dark:text-white/90">
          {title}
        </h3>
        {desc && (
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            {desc}
          </p>
        )}
      </div>

      {/* Card Body */}
      <div className={`border-t border-gray-100 dark:border-gray-800 overflow-hidden ${
        scrollable ? "flex-1" : "p-4 sm:p-5"
      }`}>
        {scrollable ? (
          <div
            className="overflow-y-auto overflow-x-hidden p-3 sm:p-4 table-scrollbar"
            style={{ maxHeight }}
          >
            <div className="space-y-4">{children}</div>
          </div>
        ) : (
          <div className="space-y-4">{children}</div>
        )}
      </div>
    </div>
  );
};

export default ComponentCard;
