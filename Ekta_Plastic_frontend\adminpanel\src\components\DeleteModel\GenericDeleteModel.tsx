import { Modal } from "../ui/modal";

interface DeleteConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  UserName?: string; // Single item name
  selectedItems?: string[]; // Array of selected item names for multiple deletion
}

export default function GenericDeleteConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title = "Confirm Deletion",
  message,
  UserName = "",
  selectedItems = []
}: DeleteConfirmModalProps) {

  return (
    <Modal isOpen={isOpen} onClose={onClose} className="max-w-[450px] p-6">
      <div className="flex flex-col">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-2">{title}</h2>
        <div className="w-full h-0.5 bg-gray-100 dark:bg-gray-700 my-3"></div>

        <p className="text-gray-600 dark:text-gray-300 mb-6">
  {message ? (
    <>
      Are you sure you want to delete "
      <span className="font-bold text-blue-600 dark:text-blue-400">
        {UserName}
      </span>
      "? This action cannot be undone.
    </>
  ) : selectedItems && selectedItems.length > 0 ? (
    // Multiple items deletion message
    <>
      Are you sure you want to delete the following {selectedItems.length} items?
      <div className="mt-2 max-h-40 overflow-y-auto">
        <ul className="list-disc pl-5">
          {selectedItems.map((item, index) => (
            <li key={index} className="font-medium text-blue-600 dark:text-blue-400">
              {item}
            </li>
          ))}
        </ul>
      </div>
      <div className="mt-2">This action cannot be undone.</div>
    </>
  ) : (
    // Default message for single item
    <>
      Are you sure you want to delete {UserName ? (
        <>
          "<span className="font-bold text-blue-600 dark:text-blue-400">{UserName}</span>"
        </>
      ) : (
        "this record"
      )}?
    </>
  )}
</p>

        <div className="flex justify-end items-center gap-4 mt-2">
          <button
            onClick={onClose}
            className="px-5 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600 dark:focus:ring-gray-600 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-5 py-2.5 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300 dark:bg-red-600 dark:hover:bg-red-700 dark:focus:ring-red-800 transition-colors"
          >
            Delete
          </button>
        </div>
      </div>
    </Modal>
  );
}