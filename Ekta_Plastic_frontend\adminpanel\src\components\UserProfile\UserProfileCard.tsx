import { useState, useEffect, useRef } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "../../context/AuthContext";
import { Pencil, Camera, X } from "lucide-react";
import { userApi } from "../../services/api";

interface UserData {
  first_name: string;
  last_name: string;
  email: string;
  phone: string;
  image?: string;
  is_premium?: boolean;
}

export default function UserProfileCard() {
  const { TokenFROMLSGet } = useAuth();
  const { isOpen, openModal, closeModal } = useModal();
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Add a ref to track if this is the first render
  const isFirstRender = useRef(true);


  const [userData, setUserData] = useState<UserData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    image: "/images/user/owner.jpg" // Default image
  });

  // Add formData state for editing
  const [formData, setFormData] = useState<UserData>({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    image: ""
  });

  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [showImageEditOptions, setShowImageEditOptions] = useState(false);

  useEffect(() => {
    // Only fetch user profile on the first render
    if (isFirstRender.current) {
      isFirstRender.current = false;
      fetchUserProfile();
    }
  }, []);

  // Modify validateField to be more strict
  const validateField = (name: string, value: string) => {
    let error = "";
    switch (name) {
      case "first_name":
      case "last_name":
        if (!value || !value.trim()) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name is required.`;
        } else if (!/^[A-Za-z]{3,}$/.test(value)) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name must contain at least 3 letters.`;
        }
        break;
      case "phone":
        if (!value || !value.trim()) {
          error = "Phone number is required.";
        } else if (!/^[6-9]\d{9}$/.test(value)) {
          error = "Phone number must be a 10-digit number starting with 6-9.";
        }
        break;
      case "email":
        if (!value || !value.trim()) {
          error = "Email is required.";
        } else if (!/^[\w-.]+@[\w-]+\.[a-z]{2,}$/i.test(value)) {
          error = "Enter a valid email address.";
        }
        break;

      default:
        break;
    }
    setErrors((prev) => ({ ...prev, [name]: error }));
    return error;
  };

  // Improve validateAllFields to be more thorough
  const validateAllFields = () => {
    let valid = true;
    const fieldNames = [
      "first_name",
      "last_name",
      "phone",
      "email",

    ];

    const newErrors: { [key: string]: string } = {};

    fieldNames.forEach((field) => {
      const value = formData[field as keyof typeof formData] as string;
      const error = validateField(field, value);
      if (error) {
        valid = false;
        newErrors[field] = error;
      }
    });

    setErrors(newErrors);
    return valid;
  };

  // When opening the modal, strip the +91 prefix for the form
  const openEditModal = () => {
    // Create a copy with phone number without +91 prefix for editing
    setFormData({
      ...userData,
    });
    setImagePreview(userData.image || null);
    setErrors({}); // Reset errors when opening modal
    openModal();
  };



  const fetchUserProfile = async () => {
    try {
      const response = await userApi.getProfile();

      // Process the image path if it exists
      const userData = response.data.user;
      if (userData.image) {
        // If image path doesn't start with http, prepend the base URL
        if (!userData.image.startsWith('http')) {
          // Remove the leading slash if it exists to avoid double slashes
          const imagePath = userData.image.startsWith('/') ? userData.image.substring(1) : userData.image;
          userData.image = `http://localhost:3000/${imagePath}`;
        }
      }

      setUserData(userData);
      console.log("Processed user data:", userData);

      // Don't call checkPremiumStatus() here to avoid infinite loops
      // The AuthContext already handles premium status checking
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  // Update handleChange to modify formData instead of userData and validate
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    validateField(name, value);
  };

  // Add handleBlur for validation on field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  // Handle image selection
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB");
        return;
      }

      // Check file type
      if (!file.type.match('image.*')) {
        toast.error("Please select an image file");
        return;
      }

      setImageFile(file);
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target) {
          setImagePreview(event.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Trigger file input click
  const handleImageClick = () => {
    fileInputRef.current?.click();
  };

  // Remove selected image
  const handleRemoveImage = () => {
    setImagePreview(userData.image || null);
    setImageFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Modify handleSave to ensure validation runs before submission
  const handleSave = async (event: React.FormEvent) => {
    event.preventDefault();

    // Force validation of all fields
    if (!validateAllFields()) {
      toast.error("Please fix all errors before saving");
      return;
    }

    try {
      // Create FormData object for multipart/form-data (for image upload)
      const formDataToSend = new FormData();

      // Add all form fields
      Object.keys(formData).forEach(key => {
        if (key !== 'image') {
          formDataToSend.append(key, formData[key as keyof UserData] as string);
        }
      });

      // Add image file if it exists
      if (imageFile) {
        formDataToSend.append('image', imageFile);
        console.log("Sending image file:", imageFile.name, imageFile.size, imageFile.type);
      }

      // Log form data contents for debugging
      console.log("FormData contents:");
      for (let pair of formDataToSend.entries()) {
        console.log(pair[0] + ': ' + (pair[1] instanceof File ? 'File: ' + (pair[1] as File).name : pair[1]));
      }

      // Note: The API service automatically adds the token and sets the correct Content-Type
      const updateResponse = await userApi.updateProfile(formDataToSend);

      console.log("Response:", updateResponse.data);
      toast.success("Profile updated successfully");
      closeModal();
      fetchUserProfile();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="p-5 border border-gray-200 rounded-2xl dark:border-gray-800 lg:p-6">
      {/* Profile Header with Image and Name */}
      <div className="flex items-center mb-8">
        <div
          className="relative group w-20 h-20 overflow-hidden border-2 border-gray-200 rounded-full dark:border-gray-700 mr-4"
          onMouseEnter={() => setShowImageEditOptions(true)}
          onMouseLeave={() => setShowImageEditOptions(false)}
        >
          {userData.image ? (
            <img
              src={userData.image}
              alt={`${userData.first_name} ${userData.last_name}`}
              className="w-full h-full object-cover"
              onError={(e) => {
                console.log("Image failed to load:", userData.image);
                e.currentTarget.src = "/images/user/owner.jpg";
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-600 font-medium text-lg">
              {userData.first_name?.charAt(0)}{userData.last_name?.charAt(0)}
            </div>
          )}
          {showImageEditOptions && (
            <div
              className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center cursor-pointer"
              onClick={openEditModal}
            >
              <Camera size={24} className="text-white" />
            </div>
          )}
        </div>

        <div>
          <h2 className="text-xl font-semibold text-gray-800 dark:text-white/90">
            {userData.first_name} {userData.last_name}
          </h2>

          <p className="text-sm text-gray-500 dark:text-gray-400">
            {userData.email}
          </p>



        </div>

        <button
          onClick={openEditModal}
          className="flex items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 ml-auto"
        >
          <Pencil size={16} className="mr-1" />
          Edit Profile
        </button>
      </div>

      {/* Personal Information Section */}
      <div>
        <h3 className="text-lg font-semibold text-gray-800 dark:text-white/90 mb-6">
          Personal Information
        </h3>

        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
          <div>
            <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
              First Name
            </p>
            <p className="text-sm font-medium text-gray-800 dark:text-white/90">
              {userData.first_name}
            </p>
          </div>

          <div>
            <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
              Last Name
            </p>
            <p className="text-sm font-medium text-gray-800 dark:text-white/90">
              {userData.last_name}
            </p>
          </div>

          <div>
            <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
              Email address
            </p>
            <p className="text-sm font-medium text-gray-800 dark:text-white/90">
              {userData.email}
            </p>
          </div>

          <div>
            <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
              Phone
            </p>
            <p className="text-sm font-medium text-gray-800 dark:text-white/90">
              {userData.phone}
            </p>
          </div>


        </div>
      </div>

      {/* Edit Profile Modal */}
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] overflow-y-auto rounded-3xl bg-white p-4 dark:bg-gray-900 lg:p-11">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
              Edit Profile
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              Update your profile information and photo.
            </p>
          </div>
          <form className="flex flex-col" onSubmit={handleSave}>
            <div className="custom-scrollbar overflow-y-auto px-2 pb-3">
              {/* Profile Image Section */}
              <div className="flex flex-col items-center mb-8">
                <div className="relative w-32 h-32 mb-4">
                  <div className="w-full h-full overflow-hidden border-2 border-gray-200 rounded-full">
                    {imagePreview ? (
                      <img
                        src={imagePreview}
                        alt="Profile Preview"
                        className="w-full h-full object-cover"
                      />
                    ) : userData.image ? (
                      <img
                        src={userData.image}
                        alt="Profile"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          console.log("Modal image failed to load:", userData.image);
                          e.currentTarget.src = "/images/user/owner.jpg";
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gray-200 text-gray-600 font-medium text-xl">
                        {userData.first_name?.charAt(0)}{userData.last_name?.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="absolute bottom-0 right-0">
                    <button
                      type="button"
                      onClick={handleImageClick}
                      className="flex items-center justify-center w-10 h-10 bg-blue-600 rounded-full text-white shadow-lg"
                    >
                      <Camera size={18} />
                    </button>
                    <input
                      type="file"
                      ref={fileInputRef}
                      onChange={handleImageChange}
                      accept="image/*"
                      className="hidden"
                    />
                  </div>
                  {imagePreview && imagePreview !== userData.image && (
                    <button
                      type="button"
                      onClick={handleRemoveImage}
                      className="absolute top-0 right-0 flex items-center justify-center w-8 h-8 bg-red-500 rounded-full text-white shadow-lg"
                    >
                      <X size={16} />
                    </button>
                  )}
                </div>
                <p className="text-sm text-gray-500">
                  Click the camera icon to upload a new profile photo
                </p>
              </div>

              <div className="mt-7">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 lg:mb-6">
                  Personal Information
                </h5>

                <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                  <div className="col-span-2 lg:col-span-1">
                    <Label>First Name</Label>
                    <Input
                      type="text"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.first_name}
                      hint={errors.first_name}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Last Name</Label>
                    <Input
                      type="text"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.last_name}
                      hint={errors.last_name}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Email Address</Label>
                    <Input
                      type="text"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.email}
                      hint={errors.email}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Phone</Label>
                    <Input
                      type="text"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      maxLength={10}
                      error={!!errors.phone}
                      hint={errors.phone}
                    />
                  </div>


                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button size="sm" variant="outline" onClick={closeModal}>
                Cancel
              </Button>
              <Button size="sm" type="submit">
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
