import React, { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface GenericPerPageSelectorProps {
  itemsPerPage: number;
  onItemsPerPageChange: (value: number) => void;
  options?: number[];
  className?: string;
}

const GenericPerPageSelector: React.FC<GenericPerPageSelectorProps> = ({
  itemsPerPage,
  onItemsPerPageChange,
  options = [10, 15, 25, 50],
  className = ""
}) => {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleItemsPerPageChange = (value: number) => {
    onItemsPerPageChange(value);
    setShowDropdown(false);
  };

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <span className="text-sm text-gray-600">Show</span>
      <div className="relative">
        <button
          className="flex items-center space-x-1 px-3 py-1 border rounded-md bg-white"
          onClick={() => setShowDropdown(!showDropdown)}
        >
          <span>{itemsPerPage}</span>
          <ChevronDown size={16} />
        </button>

        {showDropdown && (
          <div className="absolute top-full left-0 mt-1 bg-white border rounded-md shadow-lg z-50">
            {options.map((value) => (
              <div
                key={value}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleItemsPerPageChange(value)}
              >
                {value}
              </div>
            ))}
          </div>
        )}
      </div>
      <span className="text-sm text-gray-600">entries</span>
    </div>
  );
};

export default GenericPerPageSelector;
