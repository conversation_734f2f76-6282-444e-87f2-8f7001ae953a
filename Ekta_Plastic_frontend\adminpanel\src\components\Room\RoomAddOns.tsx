import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface RoomAddOn {
  id?: number;
  room_id?: number;
  room_number: string;
  name: string;
  price: number | string;
  createdAt?: string;
  updatedAt?: string;
}

interface Room {
  id: number;
  room_number: string;
  hotel_id: number;
}

export const RoomAddOns = () => {
  const [roomAddOns, setRoomAddOns] = useState<RoomAddOn[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedAddOn, setSelectedAddOn] = useState<RoomAddOn | null>(null);
  const [formData, setFormData] = useState<RoomAddOn>({
    room_number: "",
    name: "",
    price: 0,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>("room_number");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Add loading state
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      // Get all room add-ons
      const response = await userApi.getAllRoomAddOns(
        currentPage,
        itemsPerPage,
        search
      );

      console.log("Room Add-ons API Response:", response.data);

      setRoomAddOns(response.data.roomAddOns || []);
      setTotalItems(
        response.data.totalCount || response.data.roomAddOns.length || 0
      );

      // Get all rooms for dropdown
      const roomsResponse = await userApi.getAllRoomNumbers(1, 1000); // Get all rooms for dropdown
      console.log("Room numbers response:", roomsResponse.data);

      // Handle different response formats
      if (roomsResponse.data && roomsResponse.data.rooms) {
        setRooms(roomsResponse.data.rooms);
      } else if (roomsResponse.data && roomsResponse.data.roomNumbers) {
        setRooms(roomsResponse.data.roomNumbers);
      } else if (roomsResponse.data && Array.isArray(roomsResponse.data)) {
        // If the API returns an array directly
        setRooms(roomsResponse.data);
      }

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      toast.error("Error fetching data");
      console.error("Error fetching data:", error);
    }
  };

  // Function to fetch room numbers separately
  const fetchRoomNumbers = async () => {
    try {
      const roomsResponse = await userApi.getAllRoomNumbers(1, 1000);
      console.log("Room numbers response (separate call):", roomsResponse.data);

      // Handle different response formats
      if (roomsResponse.data && roomsResponse.data.rooms) {
        setRooms(roomsResponse.data.rooms);
      } else if (roomsResponse.data && roomsResponse.data.roomNumbers) {
        setRooms(roomsResponse.data.roomNumbers);
      } else if (roomsResponse.data && Array.isArray(roomsResponse.data)) {
        // If the API returns an array directly
        setRooms(roomsResponse.data);
      }
    } catch (error) {
      console.error("Error fetching room numbers:", error);
      toast.error("Error fetching room numbers");
    }
  };

  // Initial data loading
  useEffect(() => {
    fetchData();
    fetchRoomNumbers(); // Fetch room numbers separately
    // Set default sorting to createdAt descending (newest first)
    setSortField("createdAt");
    setSortDirection("desc");
  }, []);

  // Reload when pagination or sorting changes
  useEffect(() => {
    fetchData();
  }, [currentPage, itemsPerPage]);

  // Handle search input change
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // Filter room add-ons based on search term
  const filteredAddOns = roomAddOns.filter((addon) => {
    const searchLower = search.toLowerCase();
    return (
      addon.room_number.toLowerCase().includes(searchLower) ||
      addon.name.toLowerCase().includes(searchLower)
    );
  });

  // Sort the filtered room add-ons
  const sortedAddOns = [...filteredAddOns].sort((a, b) => {
    if (!sortField) return 0;

    if (sortField === "room_number") {
      // Try to convert room numbers to numbers for numeric sorting
      const roomNumberA = a.room_number;
      const roomNumberB = b.room_number;

      // Check if both room numbers are numeric
      const numA = parseInt(roomNumberA);
      const numB = parseInt(roomNumberB);

      if (!isNaN(numA) && !isNaN(numB)) {
        // If both are valid numbers, sort numerically
        return sortDirection === "asc" ? numA - numB : numB - numA;
      } else {
        // Otherwise, sort alphabetically
        return sortDirection === "asc"
          ? roomNumberA.localeCompare(roomNumberB)
          : roomNumberB.localeCompare(roomNumberA);
      }
    }

    if (sortField === "name") {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();
      return sortDirection === "asc"
        ? nameA.localeCompare(nameB)
        : nameB.localeCompare(nameA);
    }

    if (sortField === "price") {
      const priceA =
        typeof a.price === "string" ? parseFloat(a.price) : a.price;
      const priceB =
        typeof b.price === "string" ? parseFloat(b.price) : b.price;
      return sortDirection === "asc" ? priceA - priceB : priceB - priceA;
    }

    if (sortField === "createdAt") {
      // For createdAt, we want to show newest first
      // If createdAt is not available, use id as a fallback (higher id = newer)
      if (a.createdAt && b.createdAt) {
        const dateA = new Date(a.createdAt).getTime();
        const dateB = new Date(b.createdAt).getTime();
        return sortDirection === "desc" ? dateB - dateA : dateA - dateB;
      } else if (a.id && b.id) {
        // Fallback to id if createdAt is not available
        return sortDirection === "desc" ? b.id - a.id : a.id - b.id;
      }
    }

    return 0;
  });

  const paginatedAddons = sortedAddOns.slice(
    ((currentPage || 1) - 1) * (itemsPerPage || sortedAddOns.length),
    (currentPage || 1) * (itemsPerPage || sortedAddOns.length)
  );
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    let newDirection: "asc" | "desc";

    if (sortField === field) {
      newDirection = sortDirection === "asc" ? "desc" : "asc";
      setSortDirection(newDirection);
    } else {
      // For createdAt, default to desc (newest first)
      // For other fields, default to asc
      newDirection = field === "createdAt" ? "desc" : "asc";
      setSortField(field);
      setSortDirection(newDirection);
    }

    // Manually sort the data since we removed the dependency from useEffect
    console.log(`Sorting by ${field} in ${newDirection} order`);
  };

  // Open modal for add or edit operation
  const openModal = (type: "add" | "edit", addon: RoomAddOn | null = null) => {
    setModalType(type);
    setSelectedAddOn(addon);
    setErrors({});

    // Log rooms data for debugging
    console.log("Available rooms for dropdown:", rooms);

    if (addon) {
      console.log("Editing room add-on:", addon);

      // For editing an existing room add-on
      setFormData({
        id: addon.id,
        room_number: addon.room_number || "",
        name: addon.name || "",
        price: addon.price || 0,
      });
    } else {
      // For adding a new room add-on - reset form with empty values
      setFormData({
        room_number: "",
        name: "",
        price: 0,
      });
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedAddOn(null);
    setModalType("");
  };

  // Validate form data
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    // Room Number validation
    if (!formData.room_number) {
      newErrors.room_number = "Room number is required";
    } else if (formData.room_number.trim() === "") {
      newErrors.room_number = "Room number cannot be empty";
    }

    // Name validation
    if (!formData.name) {
      newErrors.name = "Name is required";
    } else if (formData.name.trim() === "") {
      newErrors.name = "Name cannot be empty";
    }

    // Price validation
    const price =
      typeof formData.price === "string"
        ? parseFloat(formData.price)
        : formData.price;

    if (price <= 0) {
      newErrors.price = "Price must be greater than 0";
    } else if (isNaN(Number(price))) {
      newErrors.price = "Price must be a valid number";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Save handler for add/edit modal
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (modalType === "edit" && formData.id) {
        await userApi.updateRoomAddOn(formData.id.toString(), formData);
        toast.success("Room add-on updated successfully!");
      } else if (modalType === "add") {
        await userApi.createRoomAddOn(formData);
        toast.success("Room add-on added successfully!");

        // Reset form after successful add
        setFormData({
          room_number: "",
          name: "",
          price: 0,
        });

        // Set to first page to show newly added data at the top
        setCurrentPage(1);

        // Ensure sorting is by newest first
        setSortField("createdAt");
        setSortDirection("desc");
      }
      fetchData();
      closeModal();
    } catch (error: any) {
      console.error("Error saving room add-on:", error);

      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        toast.error(`Error: ${error.response.data.message}`);
      } else {
        toast.error("Error saving room add-on. Please try again.");
      }
    }
  };

  // Delete modal management
  const openDeleteConfirmModal = (id: number) => {
    setSelectedRows([]); // Clear any multi-selection
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const openMultiDeleteConfirmModal = () => {
    setDeletingId(null); // Clear any single selection
    setIsDeleteConfirmOpen(true);
  };

  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  // Handle selected rows from GenericTable
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        // Multiple deletion
        let successCount = 0;
        let errorCount = 0;

        // Process each selected room add-on
        for (const id of selectedRows) {
          try {
            await userApi.deleteRoomAddOn(id.toString());
            successCount++;
          } catch (error) {
            console.error(`Error deleting room add-on with ID ${id}:`, error);
            errorCount++;
          }
        }

        // Show appropriate toast messages
        if (successCount > 0) {
          toast.success(`Successfully deleted ${successCount} room add-on(s)`);
        }
        if (errorCount > 0) {
          toast.error(`Failed to delete ${errorCount} room add-on(s)`);
        }

        // Clear selection
        setSelectedRows([]);
      } else if (deletingId) {
        // Single deletion
        await userApi.deleteRoomAddOn(deletingId.toString());
        toast.success("Room add-on deleted successfully");
      } else {
        return; // Nothing to delete
      }

      fetchData();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Failed to delete room add-on(s)");
      console.error("Error deleting room add-on(s):", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Room Add-Ons | Admin Panel"
        description="Manage room add-ons in the system"
      />
      <PageBreadcrumb pageTitle="Room Add-Ons" />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <ComponentCard title="Room Add-Ons List">
          <TableControls
            search={search}
            onSearchChange={handleSearch}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            onAddClick={() => openModal("add")}
            addButtonLabel="Add Room Add-On"
          />
          <div className="w-full overflow-hidden">
            <GenericTable
              data={paginatedAddons.filter(
                (addon): addon is RoomAddOn & { id: number } =>
                  addon.id !== undefined
              )}
              columns={[
                {
                  header: "Room Number",
                  accessor: "room_number",
                  cell: (item) => (
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {item.room_number}
                    </div>
                  ),
                  sortable: true,
                },
                {
                  header: "Name",
                  accessor: "name",
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.name}
                    </div>
                  ),
                  sortable: true,
                },
                {
                  header: "Price",
                  accessor: "price",
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.price}
                    </div>
                  ),
                  sortable: true,
                },
              ]}
              onEdit={(addon) => openModal("edit", addon)}
              onDelete={(id) => openDeleteConfirmModal(id)}
              startIndex={startingSerialNumber}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              idField="id"
              showCheckboxes={true}
              onSelectedRowsChange={handleSelectedRowsChange}
              onDeleteSelected={openMultiDeleteConfirmModal}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
            />
          </div>

          {totalItems > 0 && (
            <div className="mt-2">
              <Pagination
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                startingSerialNumber={startingSerialNumber}
              />
            </div>
          )}
        </ComponentCard>
      )}

      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Room Add-On" : "Add Room Add-On"}
        onSubmit={handleSubmit}
      >
        <div className="grid grid-cols-1 gap-4 mb-4">
          {/* Room Number */}
          <div>
            <Label>
              Room Number <span className="text-black dark:text-white">*</span>
            </Label>
            <select
              name="room_number"
              value={formData.room_number}
              onChange={(e) =>
                setFormData({ ...formData, room_number: e.target.value })
              }
              className={`w-full p-3 border ${
                errors.room_number ? "border-red-500" : "border-gray-300"
              } rounded-md focus:ring-2 focus:ring-blue-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:text-white`}
            >
              <option value="">Select Room Number</option>
              {rooms && rooms.length > 0 ? (
                rooms.map((room) => (
                  <option key={room.id} value={room.room_number}>
                    {room.room_number}
                  </option>
                ))
              ) : (
                <option value="" disabled>
                  No rooms available
                </option>
              )}
            </select>
            {errors.room_number && (
              <p className="mt-1 text-sm text-red-500">{errors.room_number}</p>
            )}
          </div>

          {/* Name */}
          <div>
            <Label>
              Name <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="text"
              name="name"
              value={formData.name}
              onChange={(e) =>
                setFormData({ ...formData, name: e.target.value })
              }
              error={!!errors.name}
              hint={errors.name || ""}
            />
          </div>

          {/* Price */}
          <div>
            <Label>
              Price <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="number"
              name="price"
              value={formData.price.toString()}
              onChange={(e) =>
                setFormData({
                  ...formData,
                  price: parseFloat(e.target.value) || 0,
                })
              }
              error={!!errors.price}
              hint={errors.price || ""}
              min="0"
              step={0.01}
            />
          </div>
        </div>
      </GenericAddEditModal>

      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        UserName={
          deletingId
            ? `Room No: ${
                roomAddOns.find((addon) => addon.id === deletingId)
                  ?.room_number || ""
              }`
            : ""
        }
        selectedItems={
          selectedRows.length > 0
            ? selectedRows
                .map(
                  (id) =>
                    `Room No: ${
                      roomAddOns.find((addon) => addon.id === id)
                        ?.room_number || `#${id}`
                    }`
                )
                .filter(Boolean)
            : undefined
        }
      />
    </>
  );
};

export default RoomAddOns;
