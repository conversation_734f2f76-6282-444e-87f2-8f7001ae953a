import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../common/PageBreadCrumb";
import ComponentCard from "../common/ComponentCard";
import PageMeta from "../common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface ProductCategory {
  id?: number;
  name: string;
  isDeleted?: boolean;
  createdAt?: string;
  sr_no?: number;
}

const ProductCategoryManagement = () => {
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedCategory, setSelectedCategory] = useState<ProductCategory | null>(null);
  const [formData, setFormData] = useState<ProductCategory>({
    name: "",
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [isMultiDeleteModalOpen, setIsMultiDeleteModalOpen] = useState<boolean>(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [sortField, setSortField] = useState<string>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Fetch categories on component mount and when dependencies change
  useEffect(() => {
    fetchCategories();
  }, [currentPage, itemsPerPage, search, sortField, sortDirection]);

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      const response = await userApi.getAllProductCategories();
      
      let filteredCategories = response.data || [];

      // Filter by search
      if (search.trim()) {
        filteredCategories = filteredCategories.filter((category: ProductCategory) =>
          category.name.toLowerCase().includes(search.toLowerCase())
        );
      }

      // Sort categories
      filteredCategories.sort((a: ProductCategory, b: ProductCategory) => {
        const aValue = a[sortField as keyof ProductCategory] || "";
        const bValue = b[sortField as keyof ProductCategory] || "";
        
        if (sortDirection === "asc") {
          return aValue.toString().localeCompare(bValue.toString());
        } else {
          return bValue.toString().localeCompare(aValue.toString());
        }
      });

      setTotalItems(filteredCategories.length);
      
      // Paginate
      const startIndex = (currentPage - 1) * itemsPerPage;
      const paginatedCategories = filteredCategories.slice(startIndex, startIndex + itemsPerPage);
      
      // Add serial numbers
      const categoriesWithSerial = paginatedCategories.map((category: ProductCategory, index: number) => ({
        ...category,
        sr_no: startIndex + index + 1,
      }));

      setCategories(categoriesWithSerial);
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  // Open modal for add/edit
  const openModal = (type: "add" | "edit", category?: ProductCategory) => {
    setModalType(type);
    setSelectedCategory(category || null);
    setFormData(category ? { ...category } : { name: "" });
    setErrors({});
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setModalType("");
    setSelectedCategory(null);
    setFormData({ name: "" });
    setErrors({});
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Category name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (selectedCategory) {
        // Update existing category
        await userApi.editProductCategory(selectedCategory.id!, { name: formData.name });
        toast.success("Category updated successfully");
      } else {
        // Create new category
        await userApi.addProductCategory({ name: formData.name });
        toast.success("Category created successfully");
      }

      closeModal();
      fetchCategories();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error(selectedCategory ? "Failed to update category" : "Failed to create category");
      }
      console.error("Error saving category:", error);
    }
  };

  // Open delete confirmation modal
  const openDeleteConfirmModal = (id: number) => {
    setDeletingId(id);
    setIsDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteConfirmModal = () => {
    setIsDeleteModalOpen(false);
    setDeletingId(null);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deletingId) return;

    try {
      await userApi.deleteProductCategory(deletingId);
      toast.success("Category deleted successfully");
      closeDeleteConfirmModal();
      fetchCategories();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to delete category");
      }
      console.error("Error deleting category:", error);
    }
  };

  // Handle selected rows change
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  // Open multi-delete confirmation modal
  const openMultiDeleteConfirmModal = () => {
    if (selectedRows.length === 0) {
      toast.warning("Please select categories to delete");
      return;
    }
    setIsMultiDeleteModalOpen(true);
  };

  // Close multi-delete confirmation modal
  const closeMultiDeleteConfirmModal = () => {
    setIsMultiDeleteModalOpen(false);
  };

  // Handle multi-delete
  const handleMultiDelete = async () => {
    try {
      await Promise.all(
        selectedRows.map(id => userApi.deleteProductCategory(id))
      );
      toast.success(`${selectedRows.length} categories deleted successfully`);
      setSelectedRows([]);
      closeMultiDeleteConfirmModal();
      fetchCategories();
    } catch (error: any) {
      toast.error("Failed to delete some categories");
      console.error("Error deleting categories:", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Product Category Management"
        description="Manage product categories for your catalog"
      />
      <PageBreadcrumb pageTitle="Product Category Management" />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <ComponentCard title="Product Categories">
          <TableControls
            search={search}
            onSearchChange={handleSearch}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            onAddClick={() => openModal("add")}
            addButtonLabel="Add Category"
          />
          
          <div className="w-full overflow-hidden" style={{ position: "relative" }}>
            <GenericTable
              data={categories}
              columns={[
                {
                  header: "Category Name",
                  accessor: "name",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.name}
                    </div>
                  ),
                },
                {
                  header: "Created At",
                  accessor: "createdAt",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.createdAt ? new Date(item.createdAt).toLocaleDateString() : "-"}
                    </div>
                  ),
                },
              ]}
              onEdit={(category) => openModal("edit", category)}
              onDelete={(id) => openDeleteConfirmModal(id)}
              startIndex={startingSerialNumber}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              idField="id"
              showCheckboxes={true}
              onSelectedRowsChange={handleSelectedRowsChange}
              onDeleteSelected={openMultiDeleteConfirmModal}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
            />
          </div>

          {totalItems > 0 && (
            <div className="mt-2">
              <Pagination
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                startingSerialNumber={startingSerialNumber}
              />
            </div>
          )}
        </ComponentCard>
      )}

      {/* Add/Edit Modal */}
      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Category" : "Add Category"}
        onSubmit={handleSubmit}
      >
        <div className="space-y-4">
          <div>
            <Label htmlFor="name">Category Name *</Label>
            <Input
              id="name"
              name="name"
              type="text"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="Enter category name"
              error={!!errors.name}
              hint={errors.name}
            />
          </div>
        </div>
      </GenericAddEditModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        title="Delete Category"
        message="Are you sure you want to delete this category? This action cannot be undone."
      />

      {/* Multi-Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isMultiDeleteModalOpen}
        onClose={closeMultiDeleteConfirmModal}
        onConfirm={handleMultiDelete}
        title="Delete Categories"
        message={`Are you sure you want to delete ${selectedRows.length} selected categories? This action cannot be undone.`}
      />
    </>
  );
};

export default ProductCategoryManagement;
