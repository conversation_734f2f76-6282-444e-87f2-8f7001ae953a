import { ReactNode } from "react";
import { Modal } from "../ui/modal";

interface GenericModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  onSubmit: (e: React.FormEvent) => void;
  isSubmitting?: boolean;
  children: ReactNode;
  submitButtonText?: string;
  width?: string; // New prop to specify modal width
}

export default function GenericAddEditModal({
  isOpen,
  onClose,
  title,
  onSubmit,
  isSubmitting = false,
  children,
  submitButtonText = "Save Changes",
  width = "max-w-[600px]", // Default width
}: GenericModalProps) {
  return (
    <Modal isOpen={isOpen} onClose={onClose} className={`${width} p-5`}>
      <div className="mb-4">
        <h2 className="text-xl font-bold text-gray-800 dark:text-white">
          {title}
        </h2>
      </div>

      <form className="flex flex-col" onSubmit={onSubmit}>
        <div className="max-h-[70vh] overflow-y-auto pr-2">
          {children}
        </div>

        <div className="flex items-center gap-3 mt-6 justify-end">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {isSubmitting ? "Saving..." : submitButtonText}
          </button>
        </div>
      </form>
    </Modal>
  );
}
