import React from 'react';
import { 
  FileEdit, 
  PenSquare, 
  Pencil, 
  Edit, 
  FilePenLine, 
  FileText, 
  ClipboardEdit 
} from 'lucide-react';

const IconOptions = () => {
  return (
    <div className="p-4 bg-white rounded shadow">
      <h2 className="text-xl font-bold mb-4">Edit Icon Options</h2>
      <div className="grid grid-cols-2 gap-4">
        <div className="flex items-center p-2 border rounded">
          <FileEdit size={24} className="mr-2" />
          <span>FileEdit</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <PenSquare size={24} className="mr-2" />
          <span>PenSquare</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <Pencil size={24} className="mr-2" />
          <span>Pencil</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <Edit size={24} className="mr-2" />
          <span>Edit</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <FilePenLine size={24} className="mr-2" />
          <span>FilePenLine</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <FileText size={24} className="mr-2" />
          <span>FileText</span>
        </div>
        <div className="flex items-center p-2 border rounded">
          <ClipboardEdit size={24} className="mr-2" />
          <span>ClipboardEdit</span>
        </div>
      </div>
    </div>
  );
};

export default IconOptions;
