import React from 'react';

interface AppLogoProps {
  className?: string;
  showText?: boolean;
  size?: 'sm' | 'md' | 'lg';
  textColor?: string; // Kept for backward compatibility
  iconColor?: string;
}

const AppLogo: React.FC<AppLogoProps> = ({
  className = '',
  showText = true,
  size = 'md',
  textColor = 'text-blue-800 dark:text-white',
  iconColor = 'bg-blue-800'
}) => {
  const sizeClasses = {
    sm: 'h-8',
    md: 'h-10',
    lg: 'h-12'
  };

  return (
    <div className={`flex items-center ${className}`}>
      {showText ? (
        <div>
          <h1 className={`font-bold text-2xl tracking-tight whitespace-nowrap text-indigo-600 dark:text-indigo-400`}>
            Holiday My Trip
          </h1>
        </div>
      ) : (
        <div className="flex-shrink-0">
          <div className={`${iconColor} rounded-md text-white ${sizeClasses[size]} aspect-square flex items-center justify-center`}>
            <span className="font-bold text-base">HMT</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AppLogo;
