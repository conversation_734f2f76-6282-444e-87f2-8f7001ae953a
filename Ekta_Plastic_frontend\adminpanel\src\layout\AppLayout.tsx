import { SidebarProvider, useSidebar } from "../context/SidebarContext";
import { Outlet } from "react-router-dom";
import AppHeader from "./AppHeader";
import Backdrop from "./Backdrop";
import AppSidebar from "./AppSidebar";

const LayoutContent: React.FC = () => {
  const { isExpanded, isHovered, isMobileOpen } = useSidebar();

  return (
    <div className="min-h-screen xl:flex">
      <div className="fixed top-0 left-0 z-50 h-full">
        <AppSidebar />
        <Backdrop />
      </div>
      <div
        className={`flex-1 flex flex-col transition-all duration-300 ease-in-out bg-gray-50 dark:bg-gray-800 ${
          isExpanded || isHovered ? "lg:ml-[300px]" : "lg:ml-[90px]"
        } ${isMobileOpen ? "ml-0" : ""}`}
      >
        <div className="sticky top-0 z-40 w-full">
          <AppHeader />
        </div>
        {/* <div className="flex-1 p-4 mx-auto w-full  md:p-6"> */}
        <div className="flex-1 p-4 mx-auto w-full max-w-[var(--breakpoint-2xl)]">
          <Outlet />
        </div>
      </div>
    </div>
  );
};

const AppLayout: React.FC = () => {
  return (
    <SidebarProvider>
      <LayoutContent />
    </SidebarProvider>
  );
};

export default AppLayout;
