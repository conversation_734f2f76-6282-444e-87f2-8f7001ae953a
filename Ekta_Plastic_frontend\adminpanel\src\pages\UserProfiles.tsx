import { useState, useEffect, useRef } from "react";
import { jwtDecode } from "jwt-decode";
import { useLocation } from "react-router-dom";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import UserProfileCard from "../components/UserProfile/UserProfileCard";
import UserAddressCard from "../components/UserProfile/UserAddressCard";
import HotelInfoCard from "../components/UserProfile/HotelInfoCard";
import PageMeta from "../components/common/PageMeta";
import { useAuth } from "../context/AuthContext";
import { AlertCircle, Star } from "lucide-react";
import { userApi } from "../services/api";

export default function UserProfiles() {
  const { TokenFROMLSGet, isPremium } = useAuth();
  const location = useLocation();
  const [userRole, setUserRole] = useState<string>("");
  const [activeTab, setActiveTab] = useState<"profile" | "hotel">("profile");

  // Check URL parameters for tab selection
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tabParam = searchParams.get('tab');
    if (tabParam === 'hotel' && (userRole === 'Hotel Master' || userRole === 'Manager')) {
      setActiveTab('hotel');
    }
  }, [location.search, userRole]);

  // Add a ref to track if this is the first render
  const isFirstRender = useRef(true);

  useEffect(() => {
    // Only fetch role name on the first render
    if (isFirstRender.current) {
      isFirstRender.current = false;

      // Get user role from token
      const token = TokenFROMLSGet();
      if (token) {
        try {
          const decoded: any = jwtDecode(token);
          if (decoded.role_id) {
            fetchRoleName(decoded.role_id);
          }
        } catch (error) {
          console.error("Error decoding token:", error);
        }
      }
    }
  }, []);

  const fetchRoleName = async (roleId: number) => {
    try {
      // Convert roleId to string as the API expects a string parameter
      const response = await userApi.getRoleName(roleId.toString());

      // Access the data from the response
      if (response.data && response.data.role_name) {
        setUserRole(response.data.role_name);
      }
    } catch (error) {
      console.error("Error fetching role name:", error);
    }
  };

  const isHotelMaster = userRole === "Hotel Master" || userRole === "Manager";

  return (
    <>
      <PageMeta
        title="Profile Dashboard | Ekta Infotech Admin"
        description="User profile management page"
      />
      <PageBreadcrumb pageTitle="Profile" />

      {/* Tabs - Show Hotel tab for both Admin and Hotel Master roles */}
      {isHotelMaster && (
        <div className="mb-6 flex border-b border-gray-200 dark:border-gray-700">
          <button
            className={`mr-2 inline-block p-4 ${activeTab === "profile"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500"
              : "border-b-2 border-transparent hover:border-gray-300 hover:text-gray-600 dark:hover:text-gray-300"}`}
            onClick={() => setActiveTab("profile")}
          >
            Personal Information
          </button>
          <button
            className={`mr-2 inline-block p-4 ${activeTab === "hotel"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500"
              : "border-b-2 border-transparent hover:border-gray-300 hover:text-gray-600 dark:hover:text-gray-300"}`}
            onClick={() => setActiveTab("hotel")}
          >
            Hotel Management
          </button>
        </div>
      )}

      {/* Profile Tab Content */}
      {(activeTab === "profile" || !isHotelMaster) && (
        <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
          <h3 className="mb-5 text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-7">
            Profile
          </h3>
          <div className="space-y-6">
            <UserProfileCard />
            <UserAddressCard />
          </div>
        </div>
      )}

      {/* Hotel Management Tab Content - For both Admin and Hotel Master roles */}
      {activeTab === "hotel" && isHotelMaster && (
        <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
          <h3 className="mb-5 text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-7">
            Hotel Management
          </h3>
          <div className="space-y-6">
            {isPremium ? (
              <HotelInfoCard />
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <div className="w-20 h-20 flex items-center justify-center bg-gray-100 rounded-full mb-6">
                  <AlertCircle size={40} className="text-gray-500" />
                </div>

                <h2 className="text-xl font-bold text-gray-800 dark:text-white mb-2">
                  Premium Feature
                </h2>

                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md">
                  This feature is only available to premium users. Please upgrade your account to access all features.
                </p>

                <div className="flex flex-row gap-4">
                  <button
                    className="px-6 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                    onClick={() => setActiveTab("profile")}
                  >
                    Go to Profile
                  </button>

                  <button
                    className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center justify-center"
                  >
                    <Star size={16} className="mr-2" />
                    <span>Upgrade to Premium</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </>
  );
}
