import { useState, useEffect, useRef } from "react";
import { jwtDecode } from "jwt-decode";
import { useLocation } from "react-router-dom";
import PageBreadcrumb from "../components/common/PageBreadCrumb";
import UserProfileCard from "../components/UserProfile/UserProfileCard";

import PageMeta from "../components/common/PageMeta";
import { useAuth } from "../context/AuthContext";

// import { userApi } from "../services/api";

export default function UserProfiles() {
  const { TokenFROMLSGet } = useAuth();


  const [activeTab, setActiveTab] = useState<"profile" | "hotel">("profile");


  // Add a ref to track if this is the first render
  const isFirstRender = useRef(true);

  // useEffect(() => {
  //   // Only fetch role name on the first render
  //   if (isFirstRender.current) {
  //     isFirstRender.current = false;

  //     // Get user role from token
  //     const token = TokenFROMLSGet();
  //     if (token) {
  //       try {
  //         const decoded: any = jwtDecode(token);
  //         if (decoded.role_id) {
  //           fetchRoleName(decoded.role_id);
  //         }
  //       } catch (error) {
  //         console.error("Error decoding token:", error);
  //       }
  //     }
  //   }
  // }, []);



  return (
    <>
      <PageMeta
        title="Profile Dashboard | Ekta Infotech Admin"
        description="User profile management page"
      />
      <PageBreadcrumb pageTitle="Profile" />

 
        <div className="mb-6 flex border-b border-gray-200 dark:border-gray-700">
          <button
            className={`mr-2 inline-block p-4 ${activeTab === "profile"
              ? "border-b-2 border-blue-600 text-blue-600 dark:border-blue-500 dark:text-blue-500"
              : "border-b-2 border-transparent hover:border-gray-300 hover:text-gray-600 dark:hover:text-gray-300"}`}
            onClick={() => setActiveTab("profile")}
          >
            Personal Information
          </button>
       
        </div>


      {/* Profile Tab Content */}
  
        <div className="rounded-2xl border border-gray-200 bg-white p-5 dark:border-gray-800 dark:bg-white/[0.03] lg:p-6">
          <h3 className="mb-5 text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-7">
            Profile
          </h3>
          <div className="space-y-6">
            <UserProfileCard />
           
          </div>
        </div>    
   
    </>
  );
}
