import { useState, useEffect } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../common/PageBreadCrumb";
import PageMeta from "../common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import ComponentCard from "../common/ComponentCard";
import { TableControls } from "../common/CrudControls";

interface Amenity {
  id: number;
  name: string;
  status: boolean;
  createdAt: string;
}

export const Amenities = () => {
  // State for amenities data
  const [amenities, setAmenities] = useState<Amenity[]>([]);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(15);
  const [search, setSearch] = useState<string>("");
  const [sortField, setSortField] = useState<string>("name");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  // State for modals
  const [isAddEditModalOpen, setIsAddEditModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [selectedAmenity, setSelectedAmenity] = useState<Amenity | null>(null);
  const [formData, setFormData] = useState<{ name: string; status: boolean }>({
    name: "",
    status: true,
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  // Calculate starting index for serial numbers
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Fetch amenities on component mount and when dependencies change
  useEffect(() => {
    fetchAmenities();
  }, [currentPage, itemsPerPage, search, sortField, sortDirection]);

  // Fetch amenities from API
  const fetchAmenities = async () => {
    try {
      const response = await userApi.getAllAmenities();

      // Filter and sort amenities based on search and sort settings
      let filteredAmenities = response.data.amenities || [];

      // Apply search filter
      if (search) {
        const searchLower = search.toLowerCase();

        filteredAmenities = filteredAmenities.filter((amenity: Amenity) => {
          // Check if name matches search term
          const nameMatches = amenity.name.toLowerCase().includes(searchLower);

          // Check if status matches search term
          const statusText = amenity.status ? "active" : "inactive";
          // For status, we want exact matching if the search term is exactly 'active' or 'inactive'
          const statusMatches =
            searchLower === "active"
              ? amenity.status
              : searchLower === "inactive"
              ? !amenity.status
              : statusText.includes(searchLower);

          return nameMatches || statusMatches;
        });
      }

      // Apply sorting
      filteredAmenities.sort((a: Amenity, b: Amenity) => {
        if (sortField === "name") {
          const comparison = a.name.localeCompare(b.name);
          return sortDirection === "asc" ? comparison : -comparison;
        }
        return 0;
      });

      // Set total count for pagination
      setTotalItems(filteredAmenities.length);

      // Apply pagination
      const start = (currentPage - 1) * itemsPerPage;
      const end = start + itemsPerPage;
      const paginatedAmenities = filteredAmenities.slice(start, end);

      setAmenities(paginatedAmenities);
    } catch (error) {
      console.error("Error fetching amenities:", error);
      toast.error("Failed to load amenities");
    }
  };

  // Handle sort toggle
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearch(searchValue);
    setCurrentPage(1); // Reset to first page on new search
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // Open add modal
  const handleAdd = () => {
    setSelectedAmenity(null);
    setFormData({
      name: "",
      status: true,
    });
    setErrors({});
    setIsAddEditModalOpen(true);
  };

  // Open edit modal
  const handleEdit = (amenity: Amenity) => {
    setSelectedAmenity(amenity);
    setFormData({
      name: amenity.name,
      status: amenity.status,
    });
    setErrors({});
    setIsAddEditModalOpen(true);
  };

  // Open delete confirmation modal for single item
  const handleDeleteClick = (id: number) => {
    setSelectedRows([]); // Clear any multi-selection
    setDeletingId(id);
    setIsDeleteModalOpen(true);
  };

  // Open delete confirmation modal for multiple items
  const openMultiDeleteConfirmModal = () => {
    setDeletingId(null); // Clear any single selection
    setIsDeleteModalOpen(true);
  };

  // Handle selected rows from GenericTable
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  // Close modals
  const closeAddEditModal = () => {
    setIsAddEditModalOpen(false);
  };

  const closeDeleteModal = () => {
    setIsDeleteModalOpen(false);
    setDeletingId(null);
  };

  // Form validation
  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = "Amenity name is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when field is edited
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: "",
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      if (selectedAmenity) {
        // Update existing amenity
        await userApi.updateAmenity(selectedAmenity.id.toString(), formData);
        toast.success("Amenity updated successfully");
      } else {
        // Create new amenity
        await userApi.createAmenity(formData);
        toast.success("Amenity created successfully");
      }

      closeAddEditModal();
      fetchAmenities();
    } catch (error: any) {
      if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        toast.error(error.response.data.message);
      } else {
        toast.error(
          selectedAmenity
            ? "Failed to update amenity"
            : "Failed to create amenity"
        );
      }
      console.error("Error saving amenity:", error);
    }
  };

  // Handle delete confirmation
  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        // Multiple deletion
        let successCount = 0;
        let errorCount = 0;

        // Process each selected amenity
        for (const id of selectedRows) {
          try {
            await userApi.deleteAmenity(id.toString());
            successCount++;
          } catch (error) {
            console.error(`Error deleting amenity with ID ${id}:`, error);
            errorCount++;
          }
        }

        // Show appropriate toast messages
        if (successCount > 0) {
          toast.success(
            `Successfully deleted ${successCount} amenity/amenities`
          );
        }
        if (errorCount > 0) {
          toast.error(`Failed to delete ${errorCount} amenity/amenities`);
        }

        // Clear selection
        setSelectedRows([]);
      } else if (deletingId) {
        // Single deletion
        await userApi.deleteAmenity(deletingId.toString());
        toast.success("Amenity deleted successfully");
      } else {
        return; // Nothing to delete
      }

      closeDeleteModal();
      fetchAmenities();
    } catch (error) {
      toast.error("Failed to delete amenity/amenities");
      console.error("Error deleting amenity/amenities:", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Amenities Management | Admin Panel"
        description="Manage hotel amenities"
      />
      <PageBreadcrumb pageTitle="Amenities" />

      <ComponentCard title="Amenities List">
        <TableControls
          search={search}
          onSearchChange={handleSearchChange}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          onAddClick={handleAdd}
          addButtonLabel="Add Amenity"
        />
        <div
          className="w-full overflow-hidden"
          style={{ position: "relative" }}
        >
          <GenericTable
            data={amenities}
            columns={[
              {
                header: "Name",
                accessor: "name",
                sortable: true,
              },
              {
                header: "Status",
                accessor: "status",
                cell: (item) => (
                  <span
                    className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                      item.status
                        ? "bg-green-100 text-green-800"
                        : "bg-red-100 text-red-800"
                    }`}
                  >
                    {item.status ? "Active" : "Inactive"}
                  </span>
                ),
              },
            ]}
            onEdit={handleEdit}
            onDelete={handleDeleteClick}
            startIndex={startingSerialNumber}
            onSort={handleSort}
            sortField={sortField}
            sortDirection={sortDirection}
            idField="id"
            showCheckboxes={true}
            onSelectedRowsChange={handleSelectedRowsChange}
            onDeleteSelected={openMultiDeleteConfirmModal}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
          />
        </div>

        {/* Pagination */}
        <div className="mt-2">
          <Pagination
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={setCurrentPage}
            startingSerialNumber={startingSerialNumber}
          />
        </div>
      </ComponentCard>

      {/* Add/Edit Modal */}
      <GenericAddEditModal
        isOpen={isAddEditModalOpen}
        onClose={closeAddEditModal}
        title={selectedAmenity ? "Edit Amenity" : "Add Amenity"}
        onSubmit={handleSubmit}
      >
        <div className="mb-4">
          <Label htmlFor="name">
            Amenity Name <span className="text-red-500">*</span>
          </Label>
          <Input
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            placeholder="Enter amenity name"
            error={!!errors.name}
            hint={errors.name}
          />
        </div>

        <div className="mb-4">
          <Label htmlFor="status">
            Status <span className="text-black dark:text-white">*</span>
          </Label>
          <div className="flex items-center p-3 border border-gray-300 rounded-md bg-white dark:bg-gray-700 dark:border-gray-600">
            <input
              id="status"
              name="status"
              type="checkbox"
              checked={formData.status}
              onChange={handleChange}
              className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label
              htmlFor="status"
              className="ml-3 block text-base font-medium text-gray-900 dark:text-white"
            >
              Active
            </label>
          </div>
        </div>
      </GenericAddEditModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDelete}
        title={selectedRows.length > 1 ? "Delete Amenities" : "Delete Amenity"}
        message={
          selectedRows.length > 1
            ? "Are you sure you want to delete these amenities? This action cannot be undone."
            : "Are you sure you want to delete this amenity? This action cannot be undone."
        }
        UserName={
          deletingId
            ? amenities.find((a) => a.id === deletingId)?.name || ""
            : ""
        }
        selectedItems={
          selectedRows.length > 0
            ? selectedRows
                .map(
                  (id) =>
                    amenities.find((a) => a.id === id)?.name || `Amenity #${id}`
                )
                .filter(Boolean)
            : undefined
        }
      />
    </>
  );
};

export default Amenities;
