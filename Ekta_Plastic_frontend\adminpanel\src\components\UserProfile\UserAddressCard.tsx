// import { useModal } from "../../hooks/useModal";
// import { Modal } from "../ui/modal";
// import Button from "../ui/button/Button";
// import Input from "../form/input/InputField";
// import Label from "../form/Label";

// export default function UserAddressCard() {
//   const { isOpen, openModal, closeModal } = useModal();
//   const handleSave = () => {
//     // Handle save logic here
//     console.log("Saving changes...");
//     closeModal();
//   };
//   return (
//     <>
//       <div className="p-5 border border-gray-200 rounded-2xl dark:border-gray-800 lg:p-6">
//         <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
//           <div>
//             <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-6">
//               Address
//             </h4>

//             <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
//               <div>
//                 <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
//                   Country
//                 </p>
//                 <p className="text-sm font-medium text-gray-800 dark:text-white/90">
//                   United States.
//                 </p>
//               </div>

//               <div>
//                 <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
//                   City/State
//                 </p>
//                 <p className="text-sm font-medium text-gray-800 dark:text-white/90">
//                   Phoenix, Arizona, United States.
//                 </p>
//               </div>

//               <div>
//                 <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
//                   Postal Code
//                 </p>
//                 <p className="text-sm font-medium text-gray-800 dark:text-white/90">
//                   ERT 2489
//                 </p>
//               </div>

//               <div>
//                 <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
//                   TAX ID
//                 </p>
//                 <p className="text-sm font-medium text-gray-800 dark:text-white/90">
//                   *********
//                 </p>
//               </div>
//             </div>
//           </div>

//           <button
//             onClick={openModal}
//             className="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 lg:inline-flex lg:w-auto"
//           >
//             <svg
//               className="fill-current"
//               width="18"
//               height="18"
//               viewBox="0 0 18 18"
//               fill="none"
//               xmlns="http://www.w3.org/2000/svg"
//             >
//               <path
//                 fillRule="evenodd"
//                 clipRule="evenodd"
//                 d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z"
//                 fill=""
//               />
//             </svg>
//             Edit
//           </button>
//         </div>
//       </div>
//       <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
//         <div className="relative w-full p-4 overflow-y-auto bg-white no-scrollbar rounded-3xl dark:bg-gray-900 lg:p-11">
//           <div className="px-2 pr-14">
//             <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
//               Edit Address
//             </h4>
//             <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
//               Update your details to keep your profile up-to-date.
//             </p>
//           </div>
//           <form className="flex flex-col">
//             <div className="px-2 overflow-y-auto custom-scrollbar">
//               <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
//                 <div>
//                   <Label>Country</Label>
//                   <Input type="text" value="United States" />
//                 </div>

//                 <div>
//                   <Label>City/State</Label>
//                   <Input type="text" value="Arizona, United States." />
//                 </div>

//                 <div>
//                   <Label>Postal Code</Label>
//                   <Input type="text" value="ERT 2489" />
//                 </div>

//                 <div>
//                   <Label>TAX ID</Label>
//                   <Input type="text" value="*********" />
//                 </div>
//               </div>
//             </div>
//             <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
//               <Button size="sm" variant="outline" onClick={closeModal}>
//                 Close
//               </Button>
//               <Button size="sm" onClick={handleSave}>
//                 Save Changes
//               </Button>
//             </div>
//           </form>
//         </div>
//       </Modal>
//     </>
//   )
// }

import { useState, useEffect, useRef } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import Select from "../form/Select";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { PencilIcon, TrashBinIcon, PlusIcon } from "../../icons";
import { userApi } from "../../services/api";

interface Address {
  address_line1: string;
  address_line2: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
}

interface FormErrors {
  [key: string]: string | undefined;
  address_line1?: string;
  city?: string;
  state?: string;
  country?: string;
  postal_code?: string;
}
export default function UserAddressCard() {
  const { isOpen, openModal, closeModal } = useModal();
  const [modalType, setModalType] = useState<"add" | "edit">("add");
  const [hasAddress, setHasAddress] = useState<boolean>(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Country and state options
  const countryOptions = [
    { value: "United States", label: "United States" },
    { value: "Canada", label: "Canada" },
    { value: "United Kingdom", label: "United Kingdom" },
    { value: "Australia", label: "Australia" },
    { value: "India", label: "India" },
    { value: "Germany", label: "Germany" },
    { value: "France", label: "France" },
    { value: "Italy", label: "Italy" },
    { value: "Japan", label: "Japan" },
    { value: "China", label: "China" },
  ];

  // State options - will be filtered based on selected country
  const stateOptionsByCountry: Record<string, Array<{ value: string; label: string }>> = {
    "United States": [
      { value: "Alabama", label: "Alabama" },
      { value: "Alaska", label: "Alaska" },
      { value: "Arizona", label: "Arizona" },
      { value: "Arkansas", label: "Arkansas" },
      { value: "California", label: "California" },
      { value: "Colorado", label: "Colorado" },
      { value: "Connecticut", label: "Connecticut" },
      { value: "Delaware", label: "Delaware" },
      { value: "Florida", label: "Florida" },
      { value: "Georgia", label: "Georgia" },
      { value: "Hawaii", label: "Hawaii" },
      { value: "Idaho", label: "Idaho" },
      { value: "Illinois", label: "Illinois" },
      { value: "Indiana", label: "Indiana" },
      { value: "Iowa", label: "Iowa" },
      { value: "Kansas", label: "Kansas" },
      { value: "Kentucky", label: "Kentucky" },
      { value: "Louisiana", label: "Louisiana" },
      { value: "Maine", label: "Maine" },
      { value: "Maryland", label: "Maryland" },
      { value: "Massachusetts", label: "Massachusetts" },
      { value: "Michigan", label: "Michigan" },
      { value: "Minnesota", label: "Minnesota" },
      { value: "Mississippi", label: "Mississippi" },
      { value: "Missouri", label: "Missouri" },
      { value: "Montana", label: "Montana" },
      { value: "Nebraska", label: "Nebraska" },
      { value: "Nevada", label: "Nevada" },
      { value: "New Hampshire", label: "New Hampshire" },
      { value: "New Jersey", label: "New Jersey" },
      { value: "New Mexico", label: "New Mexico" },
      { value: "New York", label: "New York" },
      { value: "North Carolina", label: "North Carolina" },
      { value: "North Dakota", label: "North Dakota" },
      { value: "Ohio", label: "Ohio" },
      { value: "Oklahoma", label: "Oklahoma" },
      { value: "Oregon", label: "Oregon" },
      { value: "Pennsylvania", label: "Pennsylvania" },
      { value: "Rhode Island", label: "Rhode Island" },
      { value: "South Carolina", label: "South Carolina" },
      { value: "South Dakota", label: "South Dakota" },
      { value: "Tennessee", label: "Tennessee" },
      { value: "Texas", label: "Texas" },
      { value: "Utah", label: "Utah" },
      { value: "Vermont", label: "Vermont" },
      { value: "Virginia", label: "Virginia" },
      { value: "Washington", label: "Washington" },
      { value: "West Virginia", label: "West Virginia" },
      { value: "Wisconsin", label: "Wisconsin" },
      { value: "Wyoming", label: "Wyoming" },
    ],
    "India": [
      { value: "Andhra Pradesh", label: "Andhra Pradesh" },
      { value: "Arunachal Pradesh", label: "Arunachal Pradesh" },
      { value: "Assam", label: "Assam" },
      { value: "Bihar", label: "Bihar" },
      { value: "Chhattisgarh", label: "Chhattisgarh" },
      { value: "Goa", label: "Goa" },
      { value: "Gujarat", label: "Gujarat" },
      { value: "Haryana", label: "Haryana" },
      { value: "Himachal Pradesh", label: "Himachal Pradesh" },
      { value: "Jharkhand", label: "Jharkhand" },
      { value: "Karnataka", label: "Karnataka" },
      { value: "Kerala", label: "Kerala" },
      { value: "Madhya Pradesh", label: "Madhya Pradesh" },
      { value: "Maharashtra", label: "Maharashtra" },
      { value: "Manipur", label: "Manipur" },
      { value: "Meghalaya", label: "Meghalaya" },
      { value: "Mizoram", label: "Mizoram" },
      { value: "Nagaland", label: "Nagaland" },
      { value: "Odisha", label: "Odisha" },
      { value: "Punjab", label: "Punjab" },
      { value: "Rajasthan", label: "Rajasthan" },
      { value: "Sikkim", label: "Sikkim" },
      { value: "Tamil Nadu", label: "Tamil Nadu" },
      { value: "Telangana", label: "Telangana" },
      { value: "Tripura", label: "Tripura" },
      { value: "Uttar Pradesh", label: "Uttar Pradesh" },
      { value: "Uttarakhand", label: "Uttarakhand" },
      { value: "West Bengal", label: "West Bengal" },
    ],
    // Add more countries and states as needed
  };

  // Default state options for countries not in the list
  const defaultStateOptions = [
    { value: "Other", label: "Other" }
  ];

  // Get state options based on selected country
  const getStateOptions = () => {
    return stateOptionsByCountry[formData.country] || defaultStateOptions;
  };

  const [formData, setFormData] = useState<Address>({
    address_line1: "",
    address_line2: "",
    city: "",
    state: "",
    country: "",
    postal_code: "",
  });

  // Add a ref to track if this is the first render
  const isFirstRender = useRef(true);

  useEffect(() => {
    // Only fetch address on the first render
    if (isFirstRender.current) {
      isFirstRender.current = false;
      fetchUserAddress();
    }
  }, []);

  const fetchUserAddress = async () => {
    try {
      const response = await userApi.getAddress();

      if (response.data.address) {
        setFormData(response.data.address);
        setHasAddress(true);
      } else {
        setHasAddress(false);
      }
    } catch (error) {
      console.error("Error fetching user address:", error);
      setHasAddress(false);
      // Don't show error toast as we'll just show the add button
    }
  };

  const validateField = (name: string, value: string): string | undefined => {
    switch (name) {
      case 'address_line1':
        return value.trim() === '' ? 'Address Line 1 is required' : undefined;
      case 'city':
        return value.trim() === '' ? 'City is required' : undefined;
      case 'state':
        return value.trim() === '' ? 'State is required' : undefined;
      case 'country':
        return value.trim() === '' ? 'Country is required' : undefined;
      case 'postal_code':
        // Basic postal code validation - can be customized based on country
        if (value.trim() === '') return 'Postal Code is required';

        // Different postal code formats for different countries
        if (formData.country === 'United States') {
          // US ZIP code: 5 digits or 5+4 format
          const usZipRegex = /^\d{5}(-\d{4})?$/;
          return !usZipRegex.test(value) ? 'Invalid US ZIP code format' : undefined;
        } else if (formData.country === 'India') {
          // Indian PIN code: 6 digits
          const indPinRegex = /^\d{6}$/;
          return !indPinRegex.test(value) ? 'Invalid Indian PIN code format' : undefined;
        }

        // Generic validation for other countries
        return value.length < 3 ? 'Postal code is too short' : undefined;
      default:
        return undefined;
    }
  };

  const validateAllFields = (): boolean => {
    const newErrors: FormErrors = {};
    let isValid = true;

    // Validate required fields
    const requiredFields: (keyof Address)[] = ['address_line1', 'city', 'state', 'country', 'postal_code'];

    requiredFields.forEach(field => {
      const error = validateField(field, formData[field]);
      if (error) {
        newErrors[field] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Validate the field
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  };

  const handleSelectChange = (name: keyof Address, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Validate the field
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));

    // If country changes, reset state
    if (name === 'country') {
      setFormData(prev => ({
        ...prev,
        state: ''
      }));
      setErrors(prev => ({
        ...prev,
        state: undefined
      }));
    }
  };


  const handleSave = async (event: React.FormEvent) => {
    event.preventDefault();

    // Validate all fields before submission
    if (!validateAllFields()) {
      toast.error("Please fix all errors before saving");
      return;
    }

    try {
      // Always use the update endpoint which handles both create and update
      await userApi.updateAddress(formData);

      toast.success(modalType === "add" ? "Address added successfully" : "Address updated successfully");
      closeModal();
      fetchUserAddress();
    } catch (error) {
      console.error("Error saving address:", error);
      toast.error(`Failed to ${modalType === "add" ? "add" : "update"} address`);
    }
  };

  const handleDelete = async () => {
    try {
      await userApi.deleteAddress();

      toast.success("Address deleted successfully");
      fetchUserAddress();
    } catch (error) {
      console.error("Error deleting address:", error);
      toast.error("Failed to delete address");
    }
  };

  const openAddModal = () => {
    setModalType("add");
    setFormData({
      address_line1: "",
      address_line2: "",
      city: "",
      state: "",
      country: "",
      postal_code: "",
    });
    setErrors({});
    openModal();
  };

  const openEditModal = () => {
    setModalType("edit");
    setErrors({});
    openModal();
  };

  return (
    <>
      <div className="p-5 border border-gray-200 rounded-2xl dark:border-gray-800 lg:p-6">
        <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
          <div>
            <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-6">
              Address
            </h4>

            {hasAddress ? (
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
                <div>
                  <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                    Country
                  </p>
                  <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                    {formData.country}
                  </p>
                </div>

                <div>
                  <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                    City/State
                  </p>
                  <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                    {formData.city}, {formData.state}
                  </p>
                </div>

                <div>
                  <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                    Postal Code
                  </p>
                  <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                    {formData.postal_code}
                  </p>
                </div>

                <div>
                  <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                    Address1/Address2
                  </p>
                  <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                    {formData.address_line1}{formData.address_line2 ? `, ${formData.address_line2}` : ''}
                  </p>
                </div>
              </div>
            ) : (
              <div className="flex justify-center items-center py-4">
                {/* No address message is not shown as per requirement */}
              </div>
            )}
          </div>

          {hasAddress ? (
            <div className="flex gap-2">
              <button
                onClick={openEditModal}
                className="flex items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 lg:inline-flex"
              >
                <PencilIcon width={18} height={18} />
                Edit
              </button>
              <button
                onClick={handleDelete}
                className="flex items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-red-600 shadow-theme-xs hover:bg-gray-50 hover:text-red-700 dark:border-gray-700 dark:bg-gray-800 dark:text-red-400 dark:hover:bg-white/[0.03] dark:hover:text-red-300 lg:inline-flex"
              >
                <TrashBinIcon width={18} height={18} />
                Delete
              </button>
            </div>
          ) : (
            <button
              onClick={openAddModal}
              className="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 lg:inline-flex lg:w-auto"
            >
              <PlusIcon width={18} height={18} />
              Add Address
            </button>
          )}
        </div>
      </div>

      {/* Add/Edit Address Modal */}
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="relative w-full p-4 bg-white rounded-3xl dark:bg-gray-900 lg:p-11">
          <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
            {modalType === "add" ? "Add Address" : "Edit Address"}
          </h4>
          <form className="flex flex-col"  onSubmit={handleSave}>
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <div>
                <Label>Address Line 1 *</Label>
                <Input
                  type="text"
                  name="address_line1"
                  value={formData.address_line1}
                  onChange={handleChange}
                  error={!!errors.address_line1}
                  hint={errors.address_line1}
                />
              </div>

              <div>
                <Label>Address Line 2</Label>
                <Input
                  type="text"
                  name="address_line2"
                  value={formData.address_line2}
                  onChange={handleChange}
                />
              </div>

              <div>
                <Label>City *</Label>
                <Input
                  type="text"
                  name="city"
                  value={formData.city}
                  onChange={handleChange}
                  error={!!errors.city}
                  hint={errors.city}
                />
              </div>

              <div>
                <Label>Country *</Label>
                <Select
                  options={countryOptions}
                  placeholder="Select Country"
                  onChange={(value) => handleSelectChange('country', value)}
                  defaultValue={formData.country}
                />
                {errors.country && (
                  <p className="mt-1.5 text-xs text-error-500">{errors.country}</p>
                )}
              </div>

              <div>
                <Label>State *</Label>
                <Select
                  options={getStateOptions()}
                  placeholder="Select State"
                  onChange={(value) => handleSelectChange('state', value)}
                  defaultValue={formData.state}
                />
                {errors.state && (
                  <p className="mt-1.5 text-xs text-error-500">{errors.state}</p>
                )}
              </div>

              <div>
                <Label>Postal Code *</Label>
                <Input
                  type="text"
                  name="postal_code"
                  value={formData.postal_code}
                  onChange={handleChange}
                  error={!!errors.postal_code}
                  hint={errors.postal_code}
                />
              </div>

            </div>

            <div className="flex items-center gap-3 mt-6 lg:justify-end">
              <Button size="sm" variant="outline" onClick={closeModal}>
                Close
              </Button>
              <Button size="sm" type="submit">
                {modalType === "add" ? "Add Address" : "Save Changes"}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
