import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "./context/AuthContext";
import PremiumRequired from "./components/PremiumRequired";

const PremiumRoute = () => {
  const { isLoggedIn, isPremium, isLoading } = useAuth();
  const location = useLocation();

  // Show loading indicator or nothing while checking auth status
  if (isLoading) {
    // Return null or a loading spinner component if you have one
    return null; // This prevents flashing of content during loading
  }

  // If not logged in, redirect to signin
  if (!isLoggedIn) {
    return <Navigate to="/signin" replace />;
  }

  // If logged in but not premium, only allow access to profile and change-password pages
  // For all other routes, show premium required message
  const allowedNonPremiumPaths = ["/profile", "/change-password"];
  if (!isPremium && !allowedNonPremiumPaths.includes(location.pathname)) {
    // Instead of redirecting, show the premium required component
    return <PremiumRequired />;
  }

  // If premium or accessing allowed paths, allow access
  return <Outlet />;
};

export default PremiumRoute;
