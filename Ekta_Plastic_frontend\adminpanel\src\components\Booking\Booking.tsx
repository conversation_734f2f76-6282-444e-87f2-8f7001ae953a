import React, { useEffect, useState ,useMemo} from "react";
import { useNavigate } from "react-router-dom"; // Added for navigation
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface Booking {
  id?: number;
  customer_name: string;
  check_in: string;
  check_out: string;
  total_amount: number;
  status: string;
  rooms: RoomDetail[];
  createdAt?: string;
  updatedAt?: string;
}

interface RoomDetail {
  room_type: string;
  rate_plan: string;
  guest_count: { adult: number; child: number };
  rate: number;
  rooms: number;
}

export const Booking = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [search, setSearch] = useState<string>("");
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  const navigate = useNavigate(); // Added for navigation

  const fetchData = async () => {
    try {
      const response = await userApi.getAllBookings();
      // The backend now returns bookings as an array of objects with direct fields
      setBookings(response.data.bookings || []);
      setTotalItems(
        response.data.totalCount ||
          (response.data.bookings ? response.data.bookings.length : 0)
      );
      console.log(
        "p",
        response.data.totalCount ||
          (response.data.bookings ? response.data.bookings.length : 0)
      );
    } catch (error) {
      toast.error("Error fetching bookings");
      console.error("Error fetching bookings:", error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [currentPage, itemsPerPage, search]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    console.log("e.target.value", e.target.value);
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

const handleSort = (field: string) => {
  let newDirection: "asc" | "desc";
  const sortableFields = ["customer_name", "booking_date", "check_in", "check_out"];

  if (!sortableFields.includes(field)) return;

  if (sortField === field) {
    newDirection = sortDirection === "asc" ? "desc" : "asc";
    setSortDirection(newDirection);
  } else {
    newDirection = "asc";
    setSortField(field);
    setSortDirection(newDirection);
  }
};

const filteredData: Booking[] = useMemo(() => {
  return bookings.filter((row) =>
    search.trim() === ""
      ? true
      : row.customer_name.toLowerCase().includes(search.toLowerCase())
  );
}, [bookings, search]); // ?? dependencies tracked correctly

const sortedData: Booking[] = useMemo(() => {
  if (!sortField) return filteredData;

  return [...filteredData].sort((a, b) => {
    let aValue = (a as any)[sortField];
    let bValue = (b as any)[sortField];

    if (["booking_date", "check_in", "check_out"].includes(sortField)) {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    if (aValue === bValue) return 0;

    return sortDirection === "asc"
      ? aValue > bValue ? 1 : -1
      : aValue < bValue ? 1 : -1;
  });
}, [filteredData, sortField, sortDirection]); // ? now will work correctly



  const openDeleteConfirmModal = (id: number) => {
    setSelectedRows([]);
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        for (const id of selectedRows) {
          await userApi.deleteBooking(id.toString());
        }
        toast.success("Selected bookings deleted successfully");
        setSelectedRows([]);
      } else if (deletingId) {
        await userApi.deleteBooking(deletingId.toString());
        toast.success("Booking deleted successfully");
      }
      fetchData();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Error deleting booking(s)");
      console.error("Error deleting booking(s):", error);
    }
  };

  // Edit handler: fetch booking by id and navigate to AddBooking for edit
  const handleEdit = async (id: number) => {
    try {
      // Make sure to pass only the booking id (number or string), not an object
      const response = await userApi.getBookingById(id);

      // Debug: log the response to see what is returned
      console.log("Edit booking response:", response);

      // Check for booking object in response
      if (
        response &&
        response.status === 200 &&
        response.data &&
        response.data.booking
      ) {
        navigate(`/add-booking`, { state: { editBooking: response.data } });
      } else if (response.data && response.data.message) {
        toast.error(response.data.message);
      } else {
        toast.error("Booking not found");
      }
    } catch (error: any) {
      // Axios error: check for 404, else show generic error
      if (error.response && error.response.status === 404) {
        toast.error("Booking not found");
      } else if (
        error.response &&
        error.response.data &&
        error.response.data.message
      ) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Error fetching booking details");
      }
      console.error("Error fetching booking details:", error);
    }
  };
  const paginatedbookings = sortedData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
    
  );


  return (
    <>
      <PageMeta
        title="Bookings | Admin Panel"
        description="Manage bookings in the system"
      />
      <PageBreadcrumb pageTitle="Bookings" />
      <ComponentCard title="Bookings List">
        <TableControls
          search={search}
          onSearchChange={handleSearch}
          // totalItems={filteredData.length}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          onAddClick={() =>
            navigate("/add-booking", { state: { hideSidebar: true } })
          } // Pass state to hide sidebar
          addButtonLabel="Add Booking"
        />
        <GenericTable
         data={paginatedbookings}
          columns={[
            {
              header: "Booking Date",
              accessor: "booking_date",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.booking_date}
                </div>
              ),
              sortable: true,
            },
            {
              header: "Customer Name",
              accessor: "customer_name",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.customer_name}
                </div>
              ),
              sortable: true,
            },
            {
              header: "Check-In",
              accessor: "check_in",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.check_in}
                </div>
              ),
              sortable: true,
            },
            {
              header: "Check-Out",
              accessor: "check_out",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.check_out}
                </div>
              ),
              sortable: true,
            },
            {
              header: "Room",
              accessor: "room",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.room}
                </div>
              ),
              sortable: false,
            },
            {
              header: "Total Amount",
              accessor: "total_amount",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.total_amount !== undefined
                    ? Number(item.total_amount).toFixed(2)
                    : "0.00"}
                </div>
              ),
              sortable: true,
            },
            {
              header: "Payment Status",
              accessor: "payment_status",
              cell: (item) => (
                <div className="text-sm text-gray-700 dark:text-gray-300">
                  {item.payment_status}
                </div>
              ),
              sortable: false,
            },
          ]}
          onEdit={(row) => handleEdit(row.id)}
          onDelete={(id) => openDeleteConfirmModal(id)}
          startIndex={startingSerialNumber}
          onSort={handleSort}
          sortField={sortField}
          sortDirection={sortDirection}
          idField="id"
          showCheckboxes={true}
          onSelectedRowsChange={setSelectedRows}
          onDeleteSelected={handleDelete}
          currentPage={currentPage}           
          itemsPerPage={itemsPerPage}  
        />
        {totalItems > 0 && (
          <Pagination
            totalItems={filteredData.length}
            itemsPerPage={itemsPerPage}
            currentPage={currentPage}
            onPageChange={handlePageChange}
            startingSerialNumber={startingSerialNumber}
          />
        )}
      </ComponentCard>
      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        UserName={deletingId ? `Booking ID: ${deletingId}` : ""}
        selectedItems={
          selectedRows.length > 0
            ? selectedRows.map((id) => `Booking ID: ${id}`)
            : undefined
        }
      />
    </>
  );
};

export default Booking;
