import React, { useState, useEffect } from "react";
import { Calendar, ChevronDown, ChevronLeft, ChevronRight } from "lucide-react";
import PageBreadcrumb from "../common/PageBreadCrumb";
import ComponentCard from "../common/ComponentCard";
import PageMeta from "../common/PageMeta";
import { userApi } from "../../services/api";
import UpdateRatePopup from "../UpdateRate/UpdateRate";
import { useRoomRateSelection } from "../hooks/useRoomRateSelection";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";

const RoomRateComponent: React.FC = () => {
  const [rates, setRates] = useState<any[]>([]);
  const [roomTypes, setRoomTypes] = useState<any[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [currentDate, setCurrentDate] = useState<Date>(new Date());
  const [selectedRoomType, setSelectedRoomType] = useState<string>("all");
  const [allRoomTypeOptions, setAllRoomTypeOptions] = useState<any[]>([]);
  const [isDropdownOpen, setIsDropdownOpen] = useState<boolean>(false);
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [filterDate, setFilterDate] = useState<Date | null>(null);

  const {
    selectedDates,
    setSelectedDates,
    toggleDateSelection,
    isDateSelected,
    isPopupOpen,
    closePopup,
    selectedRoomTypeForUpdate,
    setSelectedRoomTypeForUpdate,
    clearSelectedDates,
    startDragging,
    stopDragging,
    isDragging,
  } = useRoomRateSelection();

  // Fetch room types with rates
  const loadRoomTypesWithRates = async () => {
    try {
      setLoading(true);
      const response = await userApi.getRoomRates();
      if (response.data) {
        setRoomTypes(response.data.roomTypes || []);
        setRates(response.data.rates || []);
      }
      clearSelectedDates();
    } catch (error) {
      console.error("Error fetching room types with rates:", error);
    } finally {
      setLoading(false);
    }
  };

  // Fetch all room types for dropdown
  const loadAllRoomTypes = async () => {
    try {
      const response = await userApi.getAllRoomTypes();
      if (response.data && response.data.roomTypes) {
        setAllRoomTypeOptions([
          { id: "all", name: "All" },
          ...response.data.roomTypes,
        ]);
      }
    } catch (error) {
      console.error("Error fetching all room types:", error);
    }
  };

  useEffect(() => {
    loadRoomTypesWithRates();
    loadAllRoomTypes();
  }, []);

  // Generate dates for the next 9 days
  const generateDates = () => {
    const dates = [];
    const startDate = new Date(currentDate);

    for (let i = 0; i < 9; i++) {
      const date = new Date(startDate);
      date.setDate(date.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  const dates = generateDates();

  // Navigate to previous/next week
  const navigatePrevious = () => {
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() - 9);
    setCurrentDate(newDate);
  };

  const navigateNext = () => {
    const newDate = new Date(currentDate);
    newDate.setDate(newDate.getDate() + 9);
    setCurrentDate(newDate);
  };

  // Existing formatDate function ko replace karein
  const formatDate = (date: Date) => {
    return date.getDate().toString().padStart(2, "0");
  };

  // Naya formatDateForAPI function add karein
  const formatDateForAPI = (date: Date) => {
    const day = date.getDate().toString().padStart(2, "0");
    const month = (date.getMonth() + 1).toString().padStart(2, "0");
    const year = date.getFullYear();
    return `${day}/${month}/${year}`; // DD/MM/YYYY format
  };

  // Calendar display ke liye existing formatMonth aur formatDay functions ko rakhein
  const formatDay = (date: Date) => {
    const days = ["SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"];
    return days[date.getDay()];
  };

  const formatMonth = (date: Date) => {
    const months = [
      "JAN",
      "FEB",
      "MAR",
      "APR",
      "MAY",
      "JUN",
      "JUL",
      "AUG",
      "SEP",
      "OCT",
      "NOV",
      "DEC",
    ];
    return months[date.getMonth()];
  };

  // Filter room types based on selection
  const filteredRoomTypes =
    selectedRoomType === "all"
      ? roomTypes
      : roomTypes.filter(
          (room) => room.id.toString() === selectedRoomType.toString()
        );

  // Get random availability status for demo
  // const getRandomStatus = () => {
  //   const statuses = [
  //     "available",
  //     "occupied",
  //     "maintenance",
  //     "blocked",
  //     "due_out",
  //     "dirty",
  //   ];
  //   const randomIndex = Math.floor(Math.random() * statuses.length);
  //   return statuses[randomIndex];
  // };

  const getStatusColor = () => {
    return "bg-green-100 dark:bg-green-900"; // Always show available color
  };

  // Toggle dropdown
  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  // Select room type
  const selectRoomType = (id: string, name: string) => {
    setSelectedRoomType(id);
    handleRoomTypeChange(id);
    setIsDropdownOpen(false);
  };
  const handleRoomTypeChange = (newRoomTypeId: string) => {
    setSelectedRoomType(newRoomTypeId);
    setSelectedDates([]);
    setSelectedRoomTypeForUpdate(newRoomTypeId);
  };

  // Get selected room type name
  const getSelectedRoomTypeName = () => {
    const selected = allRoomTypeOptions.find(
      (type) => type.id === selectedRoomType
    );
    return selected ? selected.name : "All";
  };

  // Get random counts for status filters
  const getStatusCounts = () => {
    return {
      all: 56,
      vacant: 64,
      occupied: 13,
      reserved: 3,
      blocked: 0,
      due_out: 4,
      dirty: 4,
    };
  };

  const statusCounts = getStatusCounts();

  // Format current date for display
  const formatCurrentDate = () => {
    const today = new Date();
    return `${today.getDate().toString().padStart(2, "0")}/${(
      today.getMonth() + 1
    )
      .toString()
      .padStart(2, "0")}/${today.getFullYear().toString().slice(2)}`;
  };

  const handleDateRemove = (dateToRemove: Date) => {
    setSelectedDates((prev) =>
      prev.filter(
        (d) =>
          !(
            d.date.toISOString() === dateToRemove.toISOString() &&
            d.room_type_id === selectedRoomTypeForUpdate
          )
      )
    );
  };
  return (
    <>
      <PageMeta
        title="Room Rates | Admin Panel"
        description="Manage Room Rates in the system"
      />

      <PageBreadcrumb pageTitle="Room Rates" />

      <ComponentCard title="Room Rates Calendar">
        {/* Top Controls Row */}
        <div className="flex items-center justify-between mb-4 border-b dark:border-gray-700 pb-2">
          <div className="flex items-center space-x-1">
            <ReactDatePicker
              selected={filterDate}
              onChange={(
                date: Date | null,
                event: React.SyntheticEvent<any> | undefined
              ) => {
                if (date) {
                  setFilterDate(date); // Update the selected date
                  setCurrentDate(date); // Update the calendar's current date
                }
              }}
              className="text-sm text-gray-600 dark:text-gray-300 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md px-2 py-1 cursor-pointer"
              dateFormat="dd/MM/yyyy"
              placeholderText={formatCurrentDate()}
              showPopperArrow={false}
              popperClassName="custom-datepicker-popper"
              portalId="root" /* Render the dropdown in the root element */
              withPortal
            />
            <Calendar
              className="text-gray-500 dark:text-gray-400 mx-1"
              size={16}
            />

            {/* Status Filters */}
            <div className="flex items-center space-x-1 ml-2">
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "all"
                    ? "bg-gray-200 dark:bg-gray-700 font-medium"
                    : "bg-gray-100 dark:bg-gray-800"
                }`}
                onClick={() => setStatusFilter("all")}
              >
                All{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.all}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "vacant"
                    ? "bg-green-200 dark:bg-green-800 font-medium"
                    : "bg-green-100 dark:bg-green-900"
                }`}
                onClick={() => setStatusFilter("vacant")}
              >
                Vacant{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.vacant}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "occupied"
                    ? "bg-blue-200 dark:bg-blue-800 font-medium"
                    : "bg-blue-100 dark:bg-blue-900"
                }`}
                onClick={() => setStatusFilter("occupied")}
              >
                Occupied{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.occupied}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "reserved"
                    ? "bg-yellow-200 dark:bg-yellow-800 font-medium"
                    : "bg-yellow-100 dark:bg-yellow-900"
                }`}
                onClick={() => setStatusFilter("reserved")}
              >
                Reserved{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.reserved}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "blocked"
                    ? "bg-purple-200 dark:bg-purple-800 font-medium"
                    : "bg-purple-100 dark:bg-purple-900"
                }`}
                onClick={() => setStatusFilter("blocked")}
              >
                Blocked{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.blocked}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "due_out"
                    ? "bg-orange-200 dark:bg-orange-800 font-medium"
                    : "bg-orange-100 dark:bg-orange-900"
                }`}
                onClick={() => setStatusFilter("due_out")}
              >
                Due Out{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.due_out}
                </span>
              </button>
              <button
                className={`px-2 py-0.5 text-xs rounded-md flex items-center ${
                  statusFilter === "dirty"
                    ? "bg-red-200 dark:bg-red-800 font-medium"
                    : "bg-red-100 dark:bg-red-900"
                }`}
                onClick={() => setStatusFilter("dirty")}
              >
                Dirty{" "}
                <span className="ml-1 bg-white dark:bg-gray-600 px-1 py-0.5 rounded-full text-xs">
                  {statusCounts.dirty}
                </span>
              </button>
            </div>
          </div>

          {/* Room Type Dropdown */}
          <div className="relative">
            <div
              className="flex items-center justify-between w-32 px-3 py-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700"
              onClick={toggleDropdown}
            >
              <span className="text-sm font-medium text-gray-700 dark:text-gray-200">
                {getSelectedRoomTypeName()}
              </span>
              <ChevronDown
                size={16}
                className="text-gray-500 dark:text-gray-400"
              />
            </div>

            {isDropdownOpen && (
              <div className="absolute z-10 w-32 mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg right-0">
                <ul className="py-1 max-h-60 overflow-auto">
                  {allRoomTypeOptions.map((type) => (
                    <li
                      key={type.id}
                      className={`px-3 py-1 text-sm cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900 ${
                        selectedRoomType === type.id
                          ? "bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300"
                          : "text-gray-700 dark:text-gray-200"
                      }`}
                      onClick={() => selectRoomType(type.id, type.name)}
                    >
                      {type.name}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 dark:border-blue-400"></div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700 border dark:border-gray-700">
              <thead className="bg-blue-50 dark:bg-gray-800">
                <tr>
                  <th
                    scope="col"
                    className="px-6 py-3 text-left text-xs font-medium text-blue-700 dark:text-blue-300 uppercase tracking-wider sticky left-0 bg-blue-50 dark:bg-gray-800 z-10 border-r dark:border-gray-700 min-w-[200px]"
                  >
                    <div className="flex items-center">
                      <span className="mr-2">+</span>
                      ROOM TYPE
                    </div>
                  </th>
                  {dates.map((date, index) => (
                    <th
                      key={index}
                      scope="col"
                      className="px-2 py-3 text-center text-xs font-medium text-blue-700 dark:text-blue-300 uppercase tracking-wider border-r dark:border-gray-700 min-w-[80px]"
                    >
                      <div className="flex flex-col items-center relative">
                        {index === 0 && (
                          <button
                            onClick={navigatePrevious}
                            className="absolute left-0 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 text-blue-600 dark:text-blue-300"
                            aria-label="Previous"
                          >
                            <ChevronLeft size={16} />
                          </button>
                        )}
                        <span>{formatDay(date)}</span>
                        <span className="font-bold">{formatDate(date)}</span>
                        <span className="text-xs">{formatMonth(date)}</span>
                        {index === dates.length - 1 && (
                          <button
                            onClick={navigateNext}
                            className="absolute right-0 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-blue-100 dark:hover:bg-blue-900 text-blue-600 dark:text-blue-300"
                            aria-label="Next"
                          >
                            <ChevronRight size={16} />
                          </button>
                        )}
                      </div>
                    </th>
                  ))}
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredRoomTypes.length > 0 ? (
                  filteredRoomTypes.map((roomType) => (
                    <tr
                      key={roomType.id}
                      className="hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white sticky left-0 bg-white dark:bg-gray-900 z-10 border-r dark:border-gray-700">
                        <div className="flex items-center">
                          <button className="mr-2 text-blue-400 hover:text-blue-600 dark:text-blue-300 dark:hover:text-blue-400">
                            +
                          </button>
                          {roomType.name}
                        </div>
                      </td>
                      {dates.map((date, dateIndex) => {
                        const isSelected = isDateSelected(roomType.id, date);
                        const dateStr = formatDateForAPI(date);
                        const rateForDate = rates.find(
                          (rate) =>
                            rate.room_type_id.toString() ===
                              roomType.id.toString() && rate.date === dateStr
                        );
                        const displayRate = rateForDate
                          ? rateForDate.base_rate
                          : roomType.base_rate;

                        return (
                          <td
                            key={dateIndex}
                            className={`px-2 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 border-r dark:border-gray-700 cursor-pointer ${
                              isSelected
                                ? "bg-blue-700 text-white"
                                : "bg-white dark:bg-gray-800"
                            }`}
                            onMouseDown={() => {
                              startDragging(); // Start dragging
                              toggleDateSelection(roomType.id, date); // Select the first date
                            }}
                            onMouseEnter={() =>
                              isDragging &&
                              toggleDateSelection(roomType.id, date)
                            } // Drag over
                            onMouseUp={stopDragging} // Stop dragging
                          >
                            <div className="flex flex-col items-center">
                              <div
                                className={`w-6 h-6 flex items-center justify-center rounded-full ${
                                  isSelected
                                    ? "bg-blue-900 text-white"
                                    : getStatusColor()
                                }`}
                              >
                                <span className="text-xs">0</span>
                              </div>
                              <div className="w-full border-t border-black dark:border-white my-2"></div>
                              <span
                                className={`font-bold ${
                                  isSelected
                                    ? "text-white"
                                    : "text-blue-600 dark:text-blue-400"
                                }`}
                              >
                                {displayRate}
                              </span>
                            </div>
                          </td>
                        );
                      })}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td
                      colSpan={dates.length + 1}
                      className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300 text-center"
                    >
                      No room types found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </ComponentCard>

      {/* Update Rate Popup */}
      <UpdateRatePopup
        isOpen={isPopupOpen}
        onClose={closePopup}
        selectedDates={selectedDates.filter(
          (d) => d.room_type_id === selectedRoomTypeForUpdate
        )}
        room_type_id={selectedRoomTypeForUpdate}
        roomTypes={roomTypes}
        onRateUpdated={loadRoomTypesWithRates}
        onDateRemove={handleDateRemove}
      />
    </>
  );
};

export default RoomRateComponent;
