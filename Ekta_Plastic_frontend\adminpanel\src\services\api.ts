import axios from 'axios';
import appConfig from '../config/appConfig';

// Create an axios instance with the base URL
const apiClient = axios.create({
  baseURL: appConfig.apiUrl,
});

// Add auth token to requestsr
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('token');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// User API functions
export const userApi = {
  // Auth
  login: (data: any) => apiClient.post('/login', data),
  register: (data: any) => apiClient.post('/register', data),
  getRoleName: (roleId: string) => apiClient.get(`/getrolename?role_id=${roleId}`),

  // User management
  getUserList: (page: number, limit: number, searchField: string, search: string, exactMatch: boolean, caseSensitive: boolean) =>
    apiClient.get(`/getuserlist?page=${page}&limit=${limit}&searchField=${searchField}&search=${search}&exactMatch=${exactMatch}&caseSensitive=${caseSensitive}`),
  addUser: (data: any) => apiClient.post('/adduser', data),
  editUser: (id: string, data: any) => apiClient.put(`/edituser/${id}`, data),
  deleteUser: (id: string) => apiClient.delete(`/deleteuser/${id}`),

  // Profile
  getProfile: () => apiClient.get('/profile'),
  updateProfile: (data: any) => apiClient.put('/profile/update', data),
  changePassword: (data: any) => apiClient.put('/profile/change-password', data),

  // Roles
  getAllRoles: () => apiClient.get('/getallroles'),
  getRoles: () => apiClient.get('/roles'),

  // Address
  getAddress: () => apiClient.get('/address'),
  createAddress: (data: any) => apiClient.post('/address', data),
  updateAddress: (data: any) => apiClient.put('/address/update', data),
  deleteAddress: () => apiClient.delete('/address'),

  // Hotel
  getHotelById: (hotelId: number | string) => apiClient.get(`/hotel/${hotelId}`),
  getUserHotel: () => apiClient.get('/user-hotel'),
  updateHotel: (id: string, data: any) => apiClient.put(`/${id}`, data),
  getAllHotelNames: () => apiClient.get('/hotels/names'),

  // Custom fields
  getCustomFields: (hotelId: string) => apiClient.get(`/${hotelId}/custom-fields`),
  createCustomField: (hotelId: string, data: any) => apiClient.post(`/${hotelId}/custom-fields`, data),
  updateCustomField: (hotelId: string, fieldId: string, data: any) => apiClient.put(`/${hotelId}/custom-fields/${fieldId}`, data),
  deleteCustomField: (hotelId: string, fieldId: string) => apiClient.delete(`/${hotelId}/custom-fields/${fieldId}`),

  // Amenities
  getAllAmenityNames: () => apiClient.get('/amenities/names'),
  getAllAmenities: () => apiClient.get('/amenities'),
  createAmenity: (data: any) => apiClient.post('/amenities', data),
  updateAmenity: (id: string, data: any) => apiClient.put(`/amenities/${id}`, data),
  deleteAmenity: (id: string) => apiClient.delete(`/amenities/${id}`),

  // Room Types
  getAllRoomTypeNames: () => apiClient.get('/room-types/names'),
  getAllRoomTypes: () => apiClient.get('/room-types'),
  createRoomType: (data: any) => apiClient.post('/room-types', data),
  updateRoomType: (id: string, data: any) => apiClient.put(`/room-types/${id}`, data),
  deleteRoomType: (id: string) => apiClient.delete(`/room-types/${id}`),

  // Rooms
  getAllRoomNumbers: (page: number = 1, limit: number = 1000) => apiClient.get(`/room-numbers?page=${page}&limit=${limit}`),
  getAllRooms: (page: number, limit: number) => apiClient.get(`/rooms?page=${page}&limit=${limit}`),
  getRoom: (id: string) => apiClient.get(`/rooms/${id}`),
  viewRoomsByRoomTypeName: (roomTypeName: string) => apiClient.get(`/viewroomsbyroomtypename/${roomTypeName}`),
  createRoom: (data: FormData) => apiClient.post('/rooms', data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  updateRoom: (id: string, data: FormData) => apiClient.put(`/rooms/${id}`, data, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  }),
  deleteRoom: (id: string) => apiClient.delete(`/rooms/${id}`),

  // Room Add-ons
  getAllRoomAddOns: (page: number, limit: number, room_number?: string) => {
    let url = `/roomaddons?page=${page}&limit=${limit}`;
    if (room_number) {
      url += `&room_number=${room_number}`;
    }
    return apiClient.get(url);
  },
  createRoomAddOn: (data: any) => apiClient.post('/roomaddons', data),
  updateRoomAddOn: (id: string, data: any) => apiClient.put(`/roomaddons/${id}`, data),
  deleteRoomAddOn: (id: string) => apiClient.delete(`/roomaddons/${id}`),

  // Room Rates
  getRoomTypesWithRates: () => apiClient.get('/room-types/rates'),
  getRoomRates: () => apiClient.get('/room-rates'),
  updateRoomRates: (data: any) => apiClient.post('/room-rates', data),

  // Booking APIs
  checkRoomAvailability: (checkIn: string, checkOut: string) =>
    apiClient.get(`/check-room-availability?check_in=${checkIn}&check_out=${checkOut}`),

  createBooking: (data: any) => apiClient.post('/bookings', data),

  // Customer APIs
  searchCustomer: (phone: string) => apiClient.get(`/search-customer?phone=${phone}`),

  // Booking Management APIs
  // getAllBookings: (page: number, limit: number, search?: string) => {
  //   let url = `/bookings?page=${page}&limit=${limit}`;
  //   if (search) {
  //     url += `&search=${encodeURIComponent(search)}`;
  //   }
  //   return apiClient.get(url);
  // },

  // getBookingById: (id: string | number) => apiClient.get(`/bookings/${id}`),
  // updateBooking: (id: string | number, data: any) => apiClient.put(`/bookings/${id}`, data),
  // deleteBooking: (id: string | number) => apiClient.delete(`/bookings/${id}`),

  // Bookings APIs
  getAllBookings: () => apiClient.get(`/bookings`),
  getBookingById: (id: string | number) => apiClient.get(`/bookings/${id}`),

  updateBooking: (id: string | number, data: any) => apiClient.put(`/bookings/${id}`, data),

  deleteBooking: (id: string | number) => apiClient.delete(`/bookings/${id}`),
  getRoomRatesByName: (roomTypeName: string, from: string, to: string) =>
    apiClient.get(`/fetchRoomRatesByName?room_type_name=${encodeURIComponent(roomTypeName)}&from=${from}&to=${to}`),
//  calculateRoomRateByCriteria: (data: {
//   bookings: {
//     room_type: string;
//     room_numbers: (string | number)[];
//   }[];
//   check_in: string;
//   check_out: string;
// }) => apiClient.post("/calculateRoomRateByCriteria", data),
calculateRoomRateByCriteria: (data: any) => apiClient.post('/calculateRoomRateByCriteria', data),



  //Customers APIs
  getAllCustomers: () => apiClient.get(`/customers`),

  createCustomer: (data: any) => apiClient.post('/customers', data),
  // getBookingById: (id: string | number) => apiClient.get(`/bookings/${id}`),

  updateCustomer: (id: string | number, data: any) => apiClient.put(`/customers/${id}`, data),

  deleteCustomer: (id: string | number) => apiClient.delete(`/customers/${id}`),

};




