// import { useState, useEffect } from "react";
// import axios from "axios";
// import PageBreadcrumb from "../../components/common/PageBreadCrumb";
// import ComponentCard from "../../components/common/ComponentCard";
// import PageMeta from "../../components/common/PageMeta";
// import BasicTableOne from "../../components/tables/BasicTables/BasicTableOne";
// import AddEditModal from "../../components/AddEditModel/AddEditModel";
// import { debounce } from "lodash";

// // Props Interface
// interface BasicTablesProps {
//   managers: any[];
// }

// export default function BasicTables({ managers }: BasicTablesProps) {
//   // ? Move useState inside the component function
//   const [isModalOpen, setIsModalOpen] = useState(false);
//   const [selectedManager, setSelectedManager] = useState<any | null>(null);
//   const [searchQuery, setSearchQuery] = useState("");
//   const [filteredManagers, setFilteredManagers] = useState(managers);

//   const handleAdd = () => {
//     setSelectedManager(null); // Open modal in Add mode
//     setIsModalOpen(true);
//   };

//   const handleEdit = (user: any) => {
//     setSelectedManager(user); // Open modal in Edit mode
//     setIsModalOpen(true);
//   };

//   useEffect(() => {
//     fetchFilteredUsers(searchQuery);
//   }, [searchQuery]);

//   const fetchFilteredUsers = debounce(async (query: any) => {
//     try {
//       const response = await axios.get(
//         `http://localhost:3000/api/users/searchuser?first_name=${query}`
//       );
//       setFilteredManagers(response.data.users);
//     } catch (error) {
//       console.error("Error fetching users:", error);
//     }
//   }, 500);

//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     setSearchQuery(e.target.value);
//   };
//   return (
//     <>
//       <PageMeta
//         title="React.js Basic Tables Dashboard | TailAdmin - Next.js Admin Dashboard Template"
//         description="This is React.js Basic Tables Dashboard page for TailAdmin - React.js Tailwind CSS Admin Dashboard Template"
//       />
//       <PageBreadcrumb pageTitle="User Management" />
//       <div className="space-y-6">
//         <ComponentCard title="User List">
//           <div className="flex justify-between items-center space-x-4">
//             {/* Search Input */}
//             <input
//               type="text"
//               placeholder="Search..."
//               value={searchQuery}
//               onChange={handleSearchChange}
//               className="px-8 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
//             />

//             {/* Add Button */}
//             <button
//               onClick={handleAdd}
//               className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
//             >
//               + Add
//             </button>
//           </div>
//           {/* Passing Data as Props to BasicTableOne */}
//           <BasicTableOne
//             filteredManagers={filteredManagers}
//             managers={managers}
//             onEdit={handleEdit}
//             onDelete={() => {}}
//           />
//         </ComponentCard>
//       </div>
//       {/* Manager Modal */}
//       <AddEditModal
//         isOpen={isModalOpen}
//         onClose={() => setIsModalOpen(false)}
//         user={selectedManager}
//       />
//     </>
//   );
// }
