import { useEffect, useState } from "react";
import { jwtDecode } from "jwt-decode";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import PageBreadcrumb from "../../components/common/PageBreadCrumb";
import ComponentCard from "../../components/common/ComponentCard";
import PageMeta from "../../components/common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { useAuth } from "../../context/AuthContext";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";

interface Customer {
  id: number;
  customer_name: string;
  phone: string;
  email: string;
  hotel_id?: number;
  createdAt?: string;
  updatedAt?: string;
}

export const CustomerManagement = () => {
  const { TokenFROMLSGet } = useAuth();
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [formData, setFormData] = useState<Partial<Customer>>({
    customer_name: "",
    phone: "",
    email: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] =
    useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [sortField, setSortField] = useState<string>("id");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  const [currentUserRole, setCurrentUserRole] = useState<string>("");
  const [currentUserHotelId, setCurrentUserHotelId] = useState<number | null>(
    null
  );

  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  const fetchCustomers = async () => {
    try {
      const response = await userApi.getAllCustomers();
      let customersData = response.data.customers || [];
      customersData = customersData.sort(
        (a: Customer, b: Customer) => b.id - a.id
      );
      setCustomers(customersData);
      setTotalItems(customersData.length);
    } catch (error) {
      toast.error("Error fetching customers");
      console.error("Error fetching customers:", error);
    }
  };

  useEffect(() => {
    const fetchCurrentUser = async () => {
      try {
        const token = TokenFROMLSGet();
        if (token) {
          const decoded: any = jwtDecode(token);
          if (decoded.hotel_id) {
            setCurrentUserHotelId(decoded.hotel_id);
            setFormData((prevData) => ({
              ...prevData,
              hotel_id: decoded.hotel_id,
            }));
          }
          if (decoded.role_id) {
            const roleResponse = await userApi.getRoleName(
              decoded.role_id.toString()
            );
            if (roleResponse.data?.role_name) {
              setCurrentUserRole(roleResponse.data.role_name);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching current user:", error);
      }
    };
    fetchCurrentUser();
  }, []);

const validateForm = () => {
  const newErrors: { [key: string]: string } = {};

  // Create a copy of formData to modify
  const validatedData = { ...formData };

  if (!validatedData.customer_name?.trim()) {
    newErrors.customer_name = "Customer name is required";
  }

  if (!validatedData.phone?.trim()) {
    newErrors.phone = "Phone number is required";
  } else if (!/^\d{10}$/.test(validatedData.phone)) {
    newErrors.phone = "Phone number must be exactly 10 digits";
  }

  if (validatedData.email && validatedData.email.trim() !== "") {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(validatedData.email)) {
      newErrors.email = "Please enter a valid email address";
    }
  }

  console.log("Validation result:", newErrors, "formData:", validatedData);
  setErrors(newErrors);
  return Object.keys(newErrors).length === 0;
};


  useEffect(() => {
    fetchCustomers();
  }, [currentUserRole, currentUserHotelId]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  const filteredCustomers = customers.filter((customer) => {
    const searchLower = search.toLowerCase();
    return (
      customer.customer_name.toLowerCase().includes(searchLower) ||
      customer.phone.toLowerCase().includes(searchLower) ||
      customer.email.toLowerCase().includes(searchLower)
    );
  });

  const sortedCustomers = [...filteredCustomers].sort((a, b) => {
    if (!sortField) return 0;
    if (sortField === "id")
      return sortDirection === "desc" ? b.id - a.id : a.id - b.id;

    const fieldA = String(a[sortField as keyof Customer]).toLowerCase();
    const fieldB = String(b[sortField as keyof Customer]).toLowerCase();
    return sortDirection === "asc"
      ? fieldA.localeCompare(fieldB)
      : fieldB.localeCompare(fieldA);
  });

  const paginatedCustomers = sortedCustomers.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
    
  );

  const handlePageChange = (page: number) => setCurrentPage(page);

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSort = (field: string) => {
    const newDirection =
      sortField === field ? (sortDirection === "asc" ? "desc" : "asc") : "asc";
    setSortField(field);
    setSortDirection(newDirection);
  };

  const openModal = (
    type: "add" | "edit",
    customer: Customer | null = null
  ) => {
    setModalType(type);
    setSelectedCustomer(customer);
    setErrors({});

    if (customer) {
      setFormData({
        customer_name: customer.customer_name,
        phone: customer.phone,
        email: customer.email,
      });
    } else {
      setFormData({
        customer_name: "",
        phone: "",
        email: "",
        hotel_id: currentUserHotelId || undefined,
      });
    }
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedCustomer(null);
    setModalType("");
  };
  const closeDeleteConfirmModal = () => {
    setIsDeleteConfirmOpen(false);
    setDeletingId(null);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
  console.log("Form submitted");
  e.preventDefault(); // Prevent default form behavior
  handleSave(formData); // Call save handler
};

const handleSave = async (formData: Partial<Customer>) => {
  // First validate the form
  const isValid = validateForm();
  console.log('Form validation result:', isValid);
  
  if (!isValid) {
    console.log('Validation failed - keeping modal open');
    return; // Don't proceed with submission
  }

  try {
    // Only proceed with API calls if validation passed
    if (modalType === "edit" && selectedCustomer?.id) {
      await userApi.updateCustomer(selectedCustomer.id.toString(), formData);
      toast.success("Customer updated successfully!");
    } else if (modalType === "add") {
      await userApi.createCustomer(formData);
      toast.success("Customer added successfully!");
    }
    
    await fetchCustomers();
    closeModal(); // Only close after successful API call
  } catch (error: any) {
    console.error("Error saving customer:", error);
    toast.error(error.response?.data?.message || "Error saving customer");
    // Keep modal open to show error
  }
};


  const openDeleteConfirmModal = (id: number) => {
    setDeletingId(id);
    setIsDeleteConfirmOpen(true);
  };

  const handleDelete = async () => {
    try {
      if (selectedRows.length > 0) {
        let successCount = 0;
        for (const id of selectedRows) {
          try {
            await userApi.deleteCustomer(id.toString());
            successCount++;
          } catch (error) {
            console.error(`Error deleting customer ${id}:`, error);
          }
        }
        if (successCount > 0)
          toast.success(`Deleted ${successCount} customers`);
        setSelectedRows([]);
      } else if (deletingId) {
        await userApi.deleteCustomer(deletingId.toString());
        toast.success("Customer deleted successfully");
      }
      fetchCustomers();
      closeDeleteConfirmModal();
    } catch (error) {
      toast.error("Error deleting customer(s)");
      console.error("Delete error:", error);
    }
  };


  return (
    <>
      <PageMeta
        title="Customer Management | Admin Panel"
        description="Manage customer information"
      />
      <PageBreadcrumb pageTitle="Customer Management" />

      <ComponentCard title="Customer List">
        <TableControls
          search={search}
          onSearchChange={handleSearch}
          itemsPerPage={itemsPerPage}
          onItemsPerPageChange={handleItemsPerPageChange}
          onAddClick={() => openModal("add")}
          addButtonLabel="Add Customer"
        />
        <div className="w-full overflow-hidden">
          <>
          
          <GenericTable
            data={paginatedCustomers}
            
            columns={[
              { header: "Name", accessor: "customer_name", sortable: true },
              { header: "Phone", accessor: "phone", sortable: true },
              { header: "Email", accessor: "email", sortable: true },
            ]}
            onEdit={(customer) => openModal("edit", customer)}
            onDelete={openDeleteConfirmModal}
            startIndex={startingSerialNumber}
            onSort={handleSort}
            sortField={sortField}
            sortDirection={sortDirection}
            idField="id"
            showCheckboxes={true}
            onSelectedRowsChange={setSelectedRows}
            onDeleteSelected={() => setIsDeleteConfirmOpen(true)}
            currentPage={currentPage}
            itemsPerPage={itemsPerPage}
          />
          </>
        </div>


        {totalItems > 0 && (
          <div className="mt-2">
            <Pagination
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              currentPage={currentPage}
              onPageChange={handlePageChange}
              startingSerialNumber={startingSerialNumber}
            />
          </div>
        )}
      </ComponentCard>

      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Customer" : "Add Customer"}
        onSubmit={handleFormSubmit}
      >
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="col-span-2">
            <Label>
              Customer Name{" "}
              <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="text"
              name="customer_name"
              value={formData.customer_name || ""}
              onChange={(e) =>
                setFormData({ ...formData, customer_name: e.target.value })
              }
              error={!!errors.customer_name}
              hint={errors.customer_name}
            />
          </div>

          <div className="col-span-1">
            <Label>
              Phone <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="tel"
              name="phone"
              maxLength={10}
              value={formData.phone || ""}
              onChange={(e) => {
                const value = e.target.value;
                if (/^\d{0,10}$/.test(value)) {
                  setFormData({ ...formData, phone: value });
                }
              }}
              error={!!errors.phone}
              hint={errors.phone}
            />
          </div>

          <div className="col-span-1">
            <Label>
              Email <span className="text-black dark:text-white">*</span>
            </Label>
            <Input
              type="email"
              name="email"
              value={formData.email || ""}
              onChange={(e) =>
                setFormData({ ...formData, email: e.target.value })
              }
              error={!!errors.email}
              hint={errors.email}
            />
          </div>
        </div>
      </GenericAddEditModal>

      <GenericDeleteConfirmModal
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        onConfirm={handleDelete}
        UserName={
          deletingId
            ? customers.find((c) => c.id === deletingId)?.customer_name || ""
            : ""
        }
        selectedItems={
          selectedRows.length > 0
            ? selectedRows.map(
                (id) =>
                  customers.find((c) => c.id === id)?.customer_name ||
                  `Customer #${id}`
              )
            : undefined
        }
      />
    </>
  );
};

export default CustomerManagement;
