import { useState, useEffect } from "react";
import { useModal } from "../../hooks/useModal";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { Pencil, Plus, Trash2 } from "lucide-react";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import { userApi } from "../../services/api";

interface Hotel {
  id?: number;
  Hotel_name: string;
  location: string;
  email: string;
  phone: string;
}

interface CustomField {
  id?: number;
  field_key: string;
  field_value: string;
}

export default function HotelInfoCard() {
  const { isOpen: isHotelModalOpen, openModal: openHotelModal, closeModal: closeHotelModal } = useModal();
  const { isOpen: isFieldModalOpen, openModal: openFieldModal, closeModal: closeFieldModal } = useModal();
  const { isOpen: isDeleteModalOpen, openModal: openDeleteModal, closeModal: closeDeleteModal } = useModal();

  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [hotelFormData, setHotelFormData] = useState<Hotel>({
    Hotel_name: "",
    location: "",
    email: "",
    phone: ""
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});

  // Custom fields state
  const [customFields, setCustomFields] = useState<CustomField[]>([]);
  const [fieldFormData, setFieldFormData] = useState<CustomField>({
    field_key: "",
    field_value: ""
  });
  const [fieldErrors, setFieldErrors] = useState<{[key: string]: string}>({});
  const [selectedFieldId, setSelectedFieldId] = useState<number | null>(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    // Fetch hotel data when component mounts
    fetchHotelData();
  }, []);

  useEffect(() => {
    // Fetch custom fields when hotel data is loaded
    if (hotel?.id) {
      fetchCustomFields();
    }
  }, [hotel]);

  const fetchHotelData = async () => {
    try {
      // Simple API call to get hotel data
      const response = await userApi.getUserHotel();

      // If we have hotels data, set the first hotel
      if (response.data && response.data.hotels && response.data.hotels.length > 0) {
        setHotel(response.data.hotels[0]);
        console.log("Hotel ID:", response.data.hotels[0].id);
      } else {
        // No hotels found
        setHotel(null);
      }
    } catch (error) {
      console.error("Error fetching hotel data:", error);
      setHotel(null);
    }
  };

  // Validate form data
  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!hotelFormData.Hotel_name.trim()) {
      newErrors.Hotel_name = "Hotel name is required";
    }

    if (!hotelFormData.location.trim()) {
      newErrors.location = "Location is required";
    }

    if (!hotelFormData.email.trim()) {
      newErrors.email = "Email is required";
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(hotelFormData.email)) {
      newErrors.email = "Invalid email format";
    }

    if (!hotelFormData.phone.trim()) {
      newErrors.phone = "Phone number is required";
    } else if (!/^[6-9]\d{9}$/.test(hotelFormData.phone.replace(/\D/g, ''))) {
      newErrors.phone = "Phone number should be 10 digits and start with 6-9";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveHotel = async (formData: Hotel) => {
    // Validate form before submission
    if (!validateForm()) {
      return;
    }

    try {
      if (hotel?.id) {
        // Update existing hotel
        await userApi.updateHotel(hotel.id.toString(), formData);
        toast.success("Hotel information updated successfully");
        fetchHotelData();
        closeHotelModal();
      }
    } catch (error) {
      console.error("Error updating hotel data:", error);
      toast.error("Failed to update hotel information");
    }
  };

  const fetchCustomFields = async () => {
    if (!hotel?.id) return;

    try {
      const response = await userApi.getCustomFields(hotel.id.toString());

      if (response.data && response.data.customFields) {
        setCustomFields(response.data.customFields);
      }
    } catch (error) {
      console.error("Error fetching custom fields:", error);
      setCustomFields([]);
    }
  };

  const validateFieldForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!fieldFormData.field_key.trim()) {
      newErrors.field_key = "Label is required";
    }

    if (!fieldFormData.field_value.trim()) {
      newErrors.field_value = "Value is required";
    }

    setFieldErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSaveField = async () => {
    if (!validateFieldForm() || !hotel?.id) {
      return;
    }

    try {
      if (isEditing && selectedFieldId) {
        // Update existing field
        await userApi.updateCustomField(hotel.id.toString(), selectedFieldId.toString(), fieldFormData);
        toast.success("Custom field updated successfully");
      } else {
        // Create new field
        await userApi.createCustomField(hotel.id.toString(), fieldFormData);
        toast.success("Custom field added successfully");
      }

      fetchCustomFields();
      closeFieldModal();
    } catch (error: any) {
      console.error("Error saving custom field:", error);
      toast.error(`Failed to save custom field: ${error.response?.data?.message || error.message}`);
    }
  };

  const handleDeleteField = async () => {
    if (!selectedFieldId || !hotel?.id) {
      return;
    }

    try {
      await userApi.deleteCustomField(hotel.id.toString(), selectedFieldId.toString());

      toast.success("Custom field deleted successfully");
      closeDeleteModal();
      fetchCustomFields();
    } catch (error: any) {
      console.error("Error deleting custom field:", error);
      toast.error("Failed to delete custom field");
    }
  };

  const openAddFieldModal = () => {
    setIsEditing(false);
    setSelectedFieldId(null);
    setFieldFormData({
      field_key: "",
      field_value: ""
    });
    setFieldErrors({});
    openFieldModal();
  };

  const openEditFieldModal = (field: CustomField) => {
    setIsEditing(true);
    setSelectedFieldId(field.id || null);
    setFieldFormData({
      field_key: field.field_key,
      field_value: field.field_value
    });
    setFieldErrors({});
    openFieldModal();
  };

  const openDeleteFieldModal = (fieldId: number) => {
    setSelectedFieldId(fieldId);
    openDeleteModal();
  };

  const openEditModal = () => {
    if (hotel) {
      // Reset form data with current hotel data
      setHotelFormData({
        Hotel_name: hotel.Hotel_name,
        location: hotel.location,
        email: hotel.email,
        phone: hotel.phone
      });
      // Clear any previous errors
      setErrors({});
      openHotelModal();
    }
  };

  return (
    <>
      <div className="p-6 bg-white rounded-xl border border-gray-100">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-800">
            Hotel Information
          </h3>
          <div className="flex space-x-2">
            <button
              onClick={openAddFieldModal}
              className="inline-flex items-center justify-center rounded-full border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              <Plus size={18} className="mr-1" />
              <span>Add New Field</span>
            </button>
            <button
              onClick={openEditModal}
              className="inline-flex items-center justify-center rounded-full border border-gray-300 bg-white px-3 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50"
            >
              <Pencil size={18} className="mr-1" />
              <span>Edit</span>
            </button>
          </div>
        </div>

        {hotel ? (
          <div>
            <div className="mb-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Hotel Name
                  </p>
                  <p className="text-base text-gray-900">
                    {hotel.Hotel_name}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Location
                  </p>
                  <p className="text-base text-gray-900">
                    {hotel.location}
                  </p>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <div className="grid grid-cols-2 gap-x-6">
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Email address
                  </p>
                  <p className="text-base text-gray-900">
                    {hotel.email}
                  </p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500 mb-1">
                    Phone
                  </p>
                  <p className="text-base text-gray-900">
                    {hotel.phone}
                  </p>
                </div>
              </div>
            </div>

            {/* Custom Fields */}
            {customFields.length > 0 && (
              <div className="mt-6 border-t pt-6">
                {/* <h4 className="text-lg font-medium text-gray-800 mb-4">Custom Fields</h4> */}
                <div className="grid grid-cols-2 gap-x-6 gap-y-4">
                  {customFields.map((field) => (
                    <div key={field.id} className="group relative">
                      <p className="text-sm font-medium text-gray-500 mb-1">
                        {field.field_key}
                      </p>
                      <div className="flex items-center">
                        <p className="text-base text-gray-900">
                          {field.field_value}
                        </p>
                        <div className="ml-2 opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                          <button
                            onClick={() => openEditFieldModal(field)}
                            className="text-blue-600 hover:text-blue-900"
                          >
                            <Pencil size={16} />
                          </button>
                          <button
                            onClick={() => openDeleteFieldModal(field.id!)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="py-4 text-center text-gray-500">
            No hotel information available.
          </div>
        )}
      </div>

      {/* Hotel Edit Modal */}
      <GenericAddEditModal
        isOpen={isHotelModalOpen}
        onClose={closeHotelModal}
        title="Edit Hotel Information"
        onSubmit={(e) => {
          e.preventDefault();
          handleSaveHotel(hotelFormData);
        }}
      >
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <Label>Hotel Name <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="Hotel_name"
              value={hotelFormData.Hotel_name}
              onChange={(e) => {
                const newValue = e.target.value;
                setHotelFormData({...hotelFormData, Hotel_name: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setErrors({...errors, Hotel_name: ""});
                } else {
                  setErrors({...errors, Hotel_name: "Hotel name is required"});
                }
              }}
              error={!!errors.Hotel_name}
              hint={errors.Hotel_name || ""}
            />
          </div>

          <div>
            <Label>Location <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="location"
              value={hotelFormData.location}
              onChange={(e) => {
                const newValue = e.target.value;
                setHotelFormData({...hotelFormData, location: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setErrors({...errors, location: ""});
                } else {
                  setErrors({...errors, location: "Location is required"});
                }
              }}
              error={!!errors.location}
              hint={errors.location || ""}
            />
          </div>

          <div>
            <Label>Email address <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="email"
              name="email"
              value={hotelFormData.email}
              onChange={(e) => {
                const newValue = e.target.value;
                setHotelFormData({...hotelFormData, email: newValue});

                // Validate email format
                if (!newValue.trim()) {
                  setErrors({...errors, email: "Email is required"});
                } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(newValue)) {
                  setErrors({...errors, email: "Invalid email format"});
                } else {
                  setErrors({...errors, email: ""});
                }
              }}
              error={!!errors.email}
              hint={errors.email || ""}
            />
          </div>

          <div>
            <Label>Phone <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="phone"
              value={hotelFormData.phone}
              onChange={(e) => {
                const newValue = e.target.value;
                setHotelFormData({...hotelFormData, phone: newValue});

                // Validate phone number
                if (!newValue.trim()) {
                  setErrors({...errors, phone: "Phone number is required"});
                } else if (!/^[6-9]\d{9}$/.test(newValue.replace(/\D/g, ''))) {
                  setErrors({...errors, phone: "Phone number should be 10 digits and start with 6-9"});
                } else {
                  setErrors({...errors, phone: ""});
                }
              }}
              error={!!errors.phone}
              maxLength={10}
              hint={errors.phone || ""}
            />
          </div>
        </div>
      </GenericAddEditModal>

      {/* Custom Field Add/Edit Modal */}
      <GenericAddEditModal
        isOpen={isFieldModalOpen}
        onClose={closeFieldModal}
        title={isEditing ? "Edit Field" : "Add New Field"}
        onSubmit={(e) => {
          e.preventDefault();
          handleSaveField();
        }}
      >
        <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
          <div>
            <Label>Label <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="field_key"
              placeholder="e.g., Pincode, Address, etc."
              value={fieldFormData.field_key}
              onChange={(e) => {
                const newValue = e.target.value;
                setFieldFormData({...fieldFormData, field_key: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setFieldErrors({...fieldErrors, field_key: ""});
                } else {
                  setFieldErrors({...fieldErrors, field_key: "Label is required"});
                }
              }}
              error={!!fieldErrors.field_key}
              hint={fieldErrors.field_key || ""}
            />
          </div>

          <div>
            <Label>Value <span className="text-black dark:text-white">*</span></Label>
            <Input
              type="text"
              name="field_value"
              placeholder="e.g., 383001, Main Street, etc."
              value={fieldFormData.field_value}
              onChange={(e) => {
                const newValue = e.target.value;
                setFieldFormData({...fieldFormData, field_value: newValue});

                // Clear error when field is valid
                if (newValue.trim()) {
                  setFieldErrors({...fieldErrors, field_value: ""});
                } else {
                  setFieldErrors({...fieldErrors, field_value: "Value is required"});
                }
              }}
              error={!!fieldErrors.field_value}
              hint={fieldErrors.field_value || ""}
            />
          </div>
        </div>
      </GenericAddEditModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteModal}
        onConfirm={handleDeleteField}
        title="Confirm Deletion"
        message="Are you sure you want to delete this custom field? This action cannot be undone."
        UserName="this custom field"
      />
    </>
  );
}