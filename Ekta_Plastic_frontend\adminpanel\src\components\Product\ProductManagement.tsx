import React, { useEffect, useState } from "react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import PageBreadcrumb from "../common/PageBreadCrumb";
import ComponentCard from "../common/ComponentCard";
import PageMeta from "../common/PageMeta";
import GenericAddEditModal from "../AddEditModel/GenericAddEditModal";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import GenericDeleteConfirmModal from "../DeleteModel/GenericDeleteModel";
import Pagination from "../Pagination/Pagination";
import { userApi } from "../../services/api";
import GenericTable from "../tables/GenericTable";
import { TableControls } from "../common/CrudControls";
import { Eye } from "lucide-react";
import GenericViewModal from "../ViewModel/GenericViewModal";

interface ProductCategory {
  id: number;
  name: string;
}

interface Product {
  id?: number;
  category_id: number;
  subcategory: string;
  image?: string;
  price: number;

  description: string;
  specification: string;
  stock?: number;
  count?: number;
  sizes: string[];
  isbestsellerproduct?: boolean;
  isDeleted?: boolean;
  createdAt?: string;
  sr_no?: number;
  ProductCategory?: ProductCategory;
}

const ProductManagement = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [search, setSearch] = useState<string>("");
  const [modalType, setModalType] = useState<"add" | "edit" | "">("");
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [formData, setFormData] = useState<Product>({
    category_id: 0,
    subcategory: "",
    image: "",
    price: 0,
  
   
    description: "",
    specification: "",
    stock: 0,
    count: 0,
    sizes: [],
    isbestsellerproduct: false,
  });
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState<boolean>(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState<boolean>(false);
  const [deletingId, setDeletingId] = useState<number | null>(null);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [isMultiDeleteModalOpen, setIsMultiDeleteModalOpen] = useState<boolean>(false);

  // Pagination states
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [sortField, setSortField] = useState<string>("subcategory");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const startingSerialNumber = (currentPage - 1) * itemsPerPage + 1;

  // Fetch products and categories on component mount
  useEffect(() => {
    fetchCategories();
    fetchProducts();
  }, [currentPage, itemsPerPage, search, sortField, sortDirection]);

  // Fetch categories from API
  const fetchCategories = async () => {
    try {
      const response = await userApi.getAllProductCategories();
      setCategories(response.data || []);
    } catch (error: any) {
      console.error("Error fetching categories:", error);
      toast.error("Failed to fetch categories");
    }
  };

  // Fetch products from API
  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const response = await userApi.getAllProducts();
      
      let filteredProducts = response.data || [];

      // Filter by search
      if (search.trim()) {
        filteredProducts = filteredProducts.filter((product: Product) =>
          product.subcategory.toLowerCase().includes(search.toLowerCase()) ||
          product.description.toLowerCase().includes(search.toLowerCase()) ||
          (product.ProductCategory?.name || "").toLowerCase().includes(search.toLowerCase())
        );
      }

      // Sort products
      filteredProducts.sort((a: Product, b: Product) => {
        let aValue: any = a[sortField as keyof Product];
        let bValue: any = b[sortField as keyof Product];
        
        // Handle nested category name sorting
        if (sortField === "category_name") {
          aValue = a.ProductCategory?.name || "";
          bValue = b.ProductCategory?.name || "";
        }
        
        if (typeof aValue === "string" && typeof bValue === "string") {
          return sortDirection === "asc" 
            ? aValue.localeCompare(bValue)
            : bValue.localeCompare(aValue);
        }
        
        if (typeof aValue === "number" && typeof bValue === "number") {
          return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
        }
        
        return 0;
      });

      setTotalItems(filteredProducts.length);
      
      // Paginate
      const startIndex = (currentPage - 1) * itemsPerPage;
      const paginatedProducts = filteredProducts.slice(startIndex, startIndex + itemsPerPage);
      
      // Add serial numbers
      const productsWithSerial = paginatedProducts.map((product: Product, index: number) => ({
        ...product,
        sr_no: startIndex + index + 1,
      }));

      setProducts(productsWithSerial);
    } catch (error: any) {
      console.error("Error fetching products:", error);
      toast.error("Failed to fetch products");
    } finally {
      setIsLoading(false);
    }
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
    setCurrentPage(1);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle sorting
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("asc");
    }
    setCurrentPage(1);
  };

  // Open modal for add/edit
  const openModal = (type: "add" | "edit", product?: Product) => {
    setModalType(type);
    setSelectedProduct(product || null);
    setFormData(product ? { ...product, sizes: product.sizes || [] } : {
      category_id: 0,
      subcategory: "",
      image: "",
      price: 0,
      description: "",
      specification: "",
      stock: 0,
      count: 0,
      sizes: [],
      isbestsellerproduct: false,
    });
    setErrors({});
    setIsModalOpen(true);
  };

  // Close modal
  const closeModal = () => {
    setIsModalOpen(false);
    setModalType("");
    setSelectedProduct(null);
    setFormData({
      category_id: 0,
      subcategory: "",
      image: "",
      price: 0,
  
      description: "",
      specification: "",
      stock: 0,
      count: 0,
      sizes: [],
      isbestsellerproduct: false,
    });
    setErrors({});
  };

  // Open view modal
  const openViewModal = (product: Product) => {
    setSelectedProduct(product);
    setIsViewModalOpen(true);
  };

  // Close view modal
  const closeViewModal = () => {
    setIsViewModalOpen(false);
    setSelectedProduct(null);
  };

  // Handle form input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    let processedValue: any = value;
    
    if (type === "number") {
      processedValue = parseFloat(value) || 0;
    } else if (type === "checkbox") {
      processedValue = (e.target as HTMLInputElement).checked;
    }
    
    setFormData(prev => ({ ...prev, [name]: processedValue }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: "" }));
    }
  };

  // Handle sizes input change
  const handleSizesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const sizesArray = e.target.value.split(",").map(size => size.trim()).filter(size => size);
    setFormData(prev => ({ ...prev, sizes: sizesArray }));
    
    if (errors.sizes) {
      setErrors(prev => ({ ...prev, sizes: "" }));
    }
  };

  // Validate form
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.category_id || formData.category_id === 0) {
      newErrors.category_id = "Category is required";
    }
    if (!formData.subcategory.trim()) {
      newErrors.subcategory = "Subcategory is required";
    }
    if (!formData.price || formData.price <= 0) {
      newErrors.price = "Valid price is required";
    }
 
    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }
    if (!formData.specification.trim()) {
      newErrors.specification = "Specification is required";
    }
    if (!formData.sizes || formData.sizes.length === 0) {
      newErrors.sizes = "At least one size is required";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const submitData = {
        ...formData,
        images: [], // Empty array as per your API structure
      };

      if (selectedProduct) {
        // Update existing product
        await userApi.editProduct(selectedProduct.id!, submitData);
        toast.success("Product updated successfully");
      } else {
        // Create new product
        await userApi.addProduct(submitData);
        toast.success("Product created successfully");
      }

      closeModal();
      fetchProducts();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error(selectedProduct ? "Failed to update product" : "Failed to create product");
      }
      console.error("Error saving product:", error);
    }
  };

  // Open delete confirmation modal
  const openDeleteConfirmModal = (id: number) => {
    setDeletingId(id);
    setIsDeleteModalOpen(true);
  };

  // Close delete confirmation modal
  const closeDeleteConfirmModal = () => {
    setIsDeleteModalOpen(false);
    setDeletingId(null);
  };

  // Handle delete
  const handleDelete = async () => {
    if (!deletingId) return;

    try {
      await userApi.deleteProduct(deletingId);
      toast.success("Product deleted successfully");
      closeDeleteConfirmModal();
      fetchProducts();
    } catch (error: any) {
      if (error.response?.data?.error) {
        toast.error(error.response.data.error);
      } else {
        toast.error("Failed to delete product");
      }
      console.error("Error deleting product:", error);
    }
  };

  // Handle selected rows change
  const handleSelectedRowsChange = (selectedIds: number[]) => {
    setSelectedRows(selectedIds);
  };

  // Open multi-delete confirmation modal
  const openMultiDeleteConfirmModal = () => {
    if (selectedRows.length === 0) {
      toast.warning("Please select products to delete");
      return;
    }
    setIsMultiDeleteModalOpen(true);
  };

  // Close multi-delete confirmation modal
  const closeMultiDeleteConfirmModal = () => {
    setIsMultiDeleteModalOpen(false);
  };

  // Handle multi-delete
  const handleMultiDelete = async () => {
    try {
      await Promise.all(
        selectedRows.map(id => userApi.deleteProduct(id))
      );
      toast.success(`${selectedRows.length} products deleted successfully`);
      setSelectedRows([]);
      closeMultiDeleteConfirmModal();
      fetchProducts();
    } catch (error: any) {
      toast.error("Failed to delete some products");
      console.error("Error deleting products:", error);
    }
  };

  return (
    <>
      <PageMeta
        title="Product Management"
        description="Manage your product catalog with full CRUD operations"
      />
      <PageBreadcrumb pageTitle="Product Management" />

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      ) : (
        <ComponentCard title="Products">
          <TableControls
            search={search}
            onSearchChange={handleSearch}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            onAddClick={() => openModal("add")}
            addButtonLabel="Add Product"
          />
          
          <div className="w-full overflow-hidden" style={{ position: "relative" }}>
            <GenericTable
              data={products}
              columns={[
                {
                  header: "Category",
                  accessor: "category_name",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.ProductCategory?.name || "-"}
                    </div>
                  ),
                },
                {
                  header: "Subcategory",
                  accessor: "subcategory",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.subcategory}
                    </div>
                  ),
                },
                {
                  header: "Price",
                  accessor: "price",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      ?{item.price}
                    </div>
                  ),
                },
       
                {
                  header: "Stock",
                  accessor: "stock",
                  sortable: true,
                  cell: (item) => (
                    <div className="text-sm text-gray-700 dark:text-gray-300">
                      {item.stock || 0}
                    </div>
                  ),
                },
                {
                  header: "Bestseller",
                  accessor: "isbestsellerproduct",
                  cell: (item) => (
                    <div className="text-sm">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        item.isbestsellerproduct 
                          ? "bg-green-100 text-green-800" 
                          : "bg-gray-100 text-gray-800"
                      }`}>
                        {item.isbestsellerproduct ? "Yes" : "No"}
                      </span>
                    </div>
                  ),
                },
              ]}
              onEdit={(product) => openModal("edit", product)}
              onDelete={(id) => openDeleteConfirmModal(id)}
              customActions={[
                {
                  icon: (
                    <Eye
                      size={16}
                      className="text-blue-600 hover:text-blue-800"
                    />
                  ),
                  tooltip: "View Product",
                  onClick: (product) => openViewModal(product),
                },
              ]}
              startIndex={startingSerialNumber}
              onSort={handleSort}
              sortField={sortField}
              sortDirection={sortDirection}
              idField="id"
              showCheckboxes={true}
              onSelectedRowsChange={handleSelectedRowsChange}
              onDeleteSelected={openMultiDeleteConfirmModal}
              currentPage={currentPage}
              itemsPerPage={itemsPerPage}
            />
          </div>

          {totalItems > 0 && (
            <div className="mt-2">
              <Pagination
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                startingSerialNumber={startingSerialNumber}
              />
            </div>
          )}
        </ComponentCard>
      )}

      {/* Add/Edit Modal */}
      <GenericAddEditModal
        isOpen={isModalOpen}
        onClose={closeModal}
        title={modalType === "edit" ? "Edit Product" : "Add Product"}
        onSubmit={handleSubmit}
      >
        <div className="space-y-4 max-h-96 overflow-y-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="category_id">Category *</Label>
              <select
                id="category_id"
                name="category_id"
                value={formData.category_id}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value={0}>Select Category</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
              {errors.category_id && (
                <p className="text-red-500 text-sm mt-1">{errors.category_id}</p>
              )}
            </div>

            <div>
              <Label htmlFor="subcategory">Subcategory *</Label>
              <Input
                id="subcategory"
                name="subcategory"
                type="text"
                value={formData.subcategory}
                onChange={handleInputChange}
                placeholder="Enter subcategory"
                error={!!errors.subcategory}
                hint={errors.subcategory}
              />
            </div>
          </div>

   

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="stock">Stock</Label>
              <Input
                id="stock"
                name="stock"
                type="number"
                value={formData.stock}
                onChange={handleInputChange}
                placeholder="Enter stock quantity"
              />
            </div>

            <div>
              <Label htmlFor="count">Count</Label>
              <Input
                id="count"
                name="count"
                type="number"
                value={formData.count}
                onChange={handleInputChange}
                placeholder="Enter count"
              />
            </div>
          </div>


          <div>
            <Label htmlFor="image">Image URL</Label>
            <Input
              id="image"
              name="image"
              type="text"
              value={formData.image}
              onChange={handleInputChange}
              placeholder="Enter image URL"
            />
          </div>

          <div>
            <Label htmlFor="sizes">Sizes * (comma-separated)</Label>
            <Input
              id="sizes"
              name="sizes"
              type="text"
              value={formData.sizes.join(", ")}
              onChange={handleSizesChange}
              placeholder="Enter sizes separated by commas (e.g., mm,ml)"
              error={!!errors.sizes}
              hint={errors.sizes}
            />
          </div>

          <div>
            <Label htmlFor="description">Description *</Label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="Enter product description"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description}</p>
            )}
          </div>

          <div>
            <Label htmlFor="specification">Specification *</Label>
            <textarea
              id="specification"
              name="specification"
              value={formData.specification}
              onChange={handleInputChange}
              placeholder="Enter product specification"
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
            {errors.specification && (
              <p className="text-red-500 text-sm mt-1">{errors.specification}</p>
            )}
          </div>

          <div className="flex items-center">
            <input
              id="isbestsellerproduct"
              name="isbestsellerproduct"
              type="checkbox"
              checked={formData.isbestsellerproduct}
              onChange={handleInputChange}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
            <Label htmlFor="isbestsellerproduct" className="ml-2">
              Is Bestseller Product
            </Label>
          </div>
        </div>
      </GenericAddEditModal>

      {/* View Modal */}
      <GenericViewModal
        isOpen={isViewModalOpen}
        onClose={closeViewModal}
        title="Product Details"
      >
        {selectedProduct && (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong>Category:</strong> {selectedProduct.ProductCategory?.name || "-"}
              </div>
              <div>
                <strong>Subcategory:</strong> {selectedProduct.subcategory}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong>Price:</strong> ?{selectedProduct.price}
              </div>
            
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <strong>Stock:</strong> {selectedProduct.stock || 0}
              </div>
              <div>
                <strong>Count:</strong> {selectedProduct.count || 0}
              </div>
            </div>


            {selectedProduct.image && (
              <div>
                <strong>Image:</strong>
                <div className="mt-2">
                  <img
                    src={selectedProduct.image}
                    alt="Product"
                    className="w-32 h-32 object-cover rounded-md"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = "/placeholder-image.png";
                    }}
                  />
                </div>
              </div>
            )}

            <div>
              <strong>Sizes:</strong> {selectedProduct.sizes?.join(", ") || "-"}
            </div>

            <div>
              <strong>Bestseller:</strong> {selectedProduct.isbestsellerproduct ? "Yes" : "No"}
            </div>

            <div>
              <strong>Description:</strong>
              <p className="mt-1 text-gray-600">{selectedProduct.description}</p>
            </div>

            <div>
              <strong>Specification:</strong>
              <p className="mt-1 text-gray-600">{selectedProduct.specification}</p>
            </div>

            {selectedProduct.createdAt && (
              <div>
                <strong>Created At:</strong> {new Date(selectedProduct.createdAt).toLocaleString()}
              </div>
            )}
          </div>
        )}
      </GenericViewModal>

      {/* Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isDeleteModalOpen}
        onClose={closeDeleteConfirmModal}
        onConfirm={handleDelete}
        title="Delete Product"
        message="Are you sure you want to delete this product? This action cannot be undone."
      />

      {/* Multi-Delete Confirmation Modal */}
      <GenericDeleteConfirmModal
        isOpen={isMultiDeleteModalOpen}
        onClose={closeMultiDeleteConfirmModal}
        onConfirm={handleMultiDelete}
        title="Delete Products"
        message={`Are you sure you want to delete ${selectedRows.length} selected products? This action cannot be undone.`}
      />
    </>
  );
};

export default ProductManagement;
