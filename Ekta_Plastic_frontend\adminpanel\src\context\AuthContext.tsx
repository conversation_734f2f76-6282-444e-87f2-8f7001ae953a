import {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { userApi } from "../services/api";

// Define types for the context
interface AuthContextType {
  isLoggedIn: boolean;
  isPremium: boolean;
  isLoading: boolean;
  storeTokenInLS: (serverToken: string) => void;
  TokenFROMLSGet: () => string | null;
  logout: () => void;
  checkPremiumStatus: () => void;
}

// Creating context with default values
export const AuthContext = createContext<AuthContextType | undefined>(
  undefined
);

// Define props for the AuthProvider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
   // Initialize token from localStorage
   const [token, setToken] = useState<string | null>(() => localStorage.getItem("token"));
   const [isPremium, setIsPremium] = useState<boolean>(false);
   const [isLoading, setIsLoading] = useState<boolean>(true);

  const isLoggedIn = Boolean(token);
  console.log("StoreisLoggedIn",isLoggedIn);

  // Check premium status when token changes
  useEffect(() => {
    if (token) {
      checkPremiumStatus();
    }
  }, [token]);

  useEffect(() => {
    const storedToken = TokenFROMLSGet();
    if (storedToken) {
      setToken(storedToken);
    } else {
      // If no token, we're not loading anymore
      setIsLoading(false);
    }
  }, []);

  // Function to store token in localStorage
  const storeTokenInLS = (serverToken: string): void => {
    setToken(serverToken);
    localStorage.setItem("token", serverToken);
  };
  // Retrieve token from localStorage
  const TokenFROMLSGet = (): string | null => {

    return localStorage.getItem("token");
  };

  // Logout function - clears localStorage and state
  const logout = (): void => {
    setToken(null);

    localStorage.removeItem("token");
  };

  // Function to check premium status from API using the userApi service
  const checkPremiumStatus = async (): Promise<void> => {
    try {
      setIsLoading(true);
      if (!token) {
        setIsLoading(false);
        return;
      }

      // Use the userApi.getProfile() method which already includes token handling
      const response = await userApi.getProfile();

      if (response.data && response.data.user) {
        setIsPremium(response.data.user.is_premium || false);
        console.log("Premium status:", response.data.user.is_premium);
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching premium status:", error);
      setIsPremium(false);
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        isLoggedIn,
        isPremium,
        isLoading,
        storeTokenInLS,
        TokenFROMLSGet,
        logout,
        checkPremiumStatus,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use AuthContext
export const useAuth = (): AuthContextType => {
  const authContextValue = useContext(AuthContext);
  if (!authContextValue) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return authContextValue;
};
