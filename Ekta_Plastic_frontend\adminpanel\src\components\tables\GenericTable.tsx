// No longer using the custom Table components
import { ChevronUp, ChevronDown, Pencil, Trash } from "lucide-react";
import { useState } from "react";

// Column definition interface
export interface ColumnDef {
  header: string;
  accessor: string;
  cell?: (item: any) => React.ReactNode;
  sortable?: boolean;
  className?: string;
}

// Custom action interface
export interface CustomAction {
  icon: React.ReactNode;
  tooltip: string;
  onClick: (item: any) => void;
}

// Props Interface
export interface GenericTableProps {
  data: any[];
  columns: ColumnDef[];
  onEdit?: (item: any) => void;
  onDelete?: (id: number) => void;
  customActions?: CustomAction[];
  startIndex: number;
  onSort?: (field: string) => void;
  sortField?: string;
  sortDirection?: "asc" | "desc";
  idField?: string;
  showCheckboxes?: boolean;
  onSelectedRowsChange?: (selectedIds: number[]) => void;
  onDeleteSelected?: () => void;
   currentPage?: number;
  itemsPerPage?: number;
}

export default function GenericTable({
  data,
  columns,
  onEdit,
  onDelete,
  customActions = [],
  // startIndex = 1,
  onSort,
  sortField = "",
  sortDirection = "asc",
  idField = "id",
  showCheckboxes = true,
  onSelectedRowsChange,
  onDeleteSelected,
    currentPage = 1,
  itemsPerPage = data.length,
}: GenericTableProps) {
  const [hoverDeleteId, setHoverDeleteId] = useState<number | null>(null);
  const [selectedRows, setSelectedRows] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSortClick = (field: string) => {
    if (onSort) {
      onSort(field);
    }
  };



  // Render sort icon for sortable columns
  const renderSortIcon = (field: string) => {
    if (sortField === field) {
      return sortDirection === "asc" ? (
        <ChevronUp className="inline-block ml-1 h-4 w-4" />
      ) : (
        <ChevronDown className="inline-block ml-1 h-4 w-4" />
      );
    }
    return null;
  };

  const toggleSelectAll = () => {
    let newSelectedRows: number[];
    if (selectAll) {
      newSelectedRows = [];
      setSelectedRows(newSelectedRows);
      setSelectAll(false);
    } else {
      newSelectedRows = data.map(item => item[idField]);
      setSelectedRows(newSelectedRows);
      setSelectAll(true);
    }

    // Notify parent component of selection change
    if (onSelectedRowsChange) {
      onSelectedRowsChange(newSelectedRows);
    }
  };

  const toggleSelectRow = (id: number) => {
    let newSelectedRows: number[];
    if (selectedRows.includes(id)) {
      newSelectedRows = selectedRows.filter((rowId) => rowId !== id);
      setSelectedRows(newSelectedRows);
      setSelectAll(false);
    } else {
      newSelectedRows = [...selectedRows, id];
      setSelectedRows(newSelectedRows);
      if (newSelectedRows.length === data.length) {
        setSelectAll(true);
      }
    }

    // Notify parent component of selection change
    if (onSelectedRowsChange) {
      onSelectedRowsChange(newSelectedRows);
    }
  };

  const handleDeleteSelected = () => {
    // If parent provided a custom delete handler, use it
    if (onDeleteSelected) {
      onDeleteSelected();
    }
    // Otherwise use default implementation
    else if (onDelete) {
      selectedRows.forEach((id) => onDelete(id));
      setSelectedRows([]);
      setSelectAll(false);

      // Notify parent component of selection change
      if (onSelectedRowsChange) {
        onSelectedRowsChange([]);
      }
    }
  };

  return (
    <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03] w-full">
      {selectedRows.length > 0 && (
        <div className="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
          <span className="text-sm text-gray-700 dark:text-gray-300">
            {selectedRows.length} item(s) selected
          </span>
          <button
            onClick={handleDeleteSelected}
            className="px-3 py-1 flex items-center gap-2 text-red-600 hover:text-red-700 transition-colors"
          >
            <Trash size={18} />
          </button>
        </div>
      )}

      <div className="w-full">
        <div className="overflow-x-auto table-scrollbar" style={{ width: "100%" }}>
          <table className="w-full border-collapse table-auto" style={{ minWidth: "1000px" }}>
          <thead className="sticky top-0 bg-white dark:bg-gray-900 z-20 shadow-sm">
            <tr className="border-b border-gray-100 dark:border-white/[0.05]">
              {showCheckboxes && (
                <th className="px-5 py-2 font-medium text-gray-500 text-theme-xs dark:text-gray-400 w-12 text-center">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={toggleSelectAll}
                    className="w-4 h-4 rounded border-gray-300 mx-auto"
                  />
                </th>
              )}
              <th className="px-5 py-2 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400">
                Sr. No
              </th>
              {columns.map((column, index) => (
                <th
                  key={index}
                  className={`px-5 py-2 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400 ${
                    column.sortable ? "cursor-pointer" : ""
                  } ${column.className || ""}`}
                  onClick={() => column.sortable && handleSortClick(column.accessor)}
                >
                  <div className="flex items-center">
                    {column.header}
                    {column.sortable && renderSortIcon(column.accessor)}
                  </div>
                </th>
              ))}
              {(onEdit || onDelete || customActions.length > 0) && (
                <th className="px-5 py-2 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400">
                  Actions
                </th>
              )}
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
            {data.length > 0 ? (
             data.map((item, index) => (
                <tr key={item[idField]}>
                  {showCheckboxes && (
                    <td className="px-5 py-2 w-12 text-center">
                      <input
                        type="checkbox"
                        checked={selectedRows.includes(item[idField])}
                        onChange={() => toggleSelectRow(item[idField])}
                        className="w-4 h-4 rounded border-gray-300 mx-auto"
                      />
                    </td>
                  )}
                  <td className="px-5 py-2 text-start">
                    <span className="block text-gray-500 text-theme-sm dark:text-gray-400">
                     {((currentPage || 1) - 1) * (itemsPerPage || data.length) + index + 1}
                    </span>
                  </td>
                  {columns.map((column, colIndex) => (
                    <td
                      key={colIndex}
                      className="px-5 py-2 text-start"
                    >
                      {column.cell
                        ? column.cell(item)
                        : <span className="block text-gray-800 text-theme-sm dark:text-gray-200">
                            {item[column.accessor]}
                          </span>
                      }
                    </td>
                  ))}
                  {(onEdit || onDelete || customActions.length > 0) && (
                    <td className="px-5 py-2 text-gray-500 text-theme-sm dark:text-gray-400">
                      <div className="flex gap-1">
                        {/* Custom actions */}
                        {customActions.map((action, actionIndex) => (
                          <button
                            key={actionIndex}
                            onClick={() => action.onClick(item)}
                            className="p-1 text-gray-700 hover:text-blue-600 transition-colors dark:text-gray-400 dark:hover:text-blue-400"
                            title={action.tooltip}
                          >
                            {action.icon}
                          </button>
                        ))}

                        {/* Edit button */}
                        {onEdit && (
                          <button
                            onClick={() => onEdit(item)}
                            className="p-1 text-gray-700 hover:text-blue-600 transition-colors dark:text-gray-400 dark:hover:text-blue-400"
                            title="Edit"
                          >
                            <Pencil size={20} />
                          </button>
                        )}

                        {/* Delete button */}
                        {onDelete && (
                          <button
                            onClick={() => onDelete(item[idField])}
                            onMouseEnter={() => setHoverDeleteId(item[idField])}
                            onMouseLeave={() => setHoverDeleteId(null)}
                            className={`p-1 ${
                              hoverDeleteId === item[idField]
                                ? "text-red-500"
                                : "text-gray-700 dark:text-gray-400"
                            } hover:text-red-600 transition-colors dark:hover:text-red-400`}
                            title="Delete"
                          >
                            <Trash size={20} />
                          </button>
                        )}
                      </div>
                    </td>
                  )}
                </tr>
              ))
            ) : (
              <tr>
                <td
                  colSpan={columns.length + (showCheckboxes ? 3 : 2)}
                  className="px-5 py-4 text-center text-gray-500 dark:text-gray-400"
                >
                  No data available
                </td>
              </tr>
            )}
          </tbody>
        </table>
        </div>
      </div>
    </div>
  );
}
