import { useState, useEffect } from "react";
import { useModal } from "../../hooks/useModal";
import { Modal } from "../ui/modal";
import Button from "../ui/button/Button";
import Input from "../form/input/InputField";
import Label from "../form/Label";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "../../context/AuthContext";
import { userApi } from "../../services/api";

interface Role {
  id: number;
  role_name: string;
}

export default function UserInfoCard() {
  const { TokenFROMLSGet } = useAuth();
  const { isOpen, openModal, closeModal } = useModal();
  // Add roles state
  const [roles, setRoles] = useState<Role[]>([]);
  const [userData, setUserData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    role_name: "",
  });

  // Add formData state for editing
  const [formData, setFormData] = useState({
    first_name: "",
    last_name: "",
    email: "",
    phone: "",
    role_name: "",
  });
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  useEffect(() => {
    fetchUserProfile();
    fetchRoles();
  }, []);

  // Modify validateField to be more strict
  const validateField = (name: string, value: string) => {
    let error = "";
    switch (name) {
      case "first_name":
      case "last_name":
        if (!value || !value.trim()) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name is required.`;
        } else if (!/^[A-Za-z]{3,}$/.test(value)) {
          error = `${
            name === "first_name" ? "First" : "Last"
          } name must contain at least 3 letters.`;
        }
        break;
      case "phone":
        if (!value || !value.trim()) {
          error = "Phone number is required.";
        } else if (!/^[6-9]\d{9}$/.test(value)) {
          error = "Phone number must be a 10-digit number starting with 6-9.";
        }
        break;
      case "email":
        if (!value || !value.trim()) {
          error = "Email is required.";
        } else if (!/^[\w-.]+@[\w-]+\.[a-z]{2,}$/i.test(value)) {
          error = "Enter a valid email address.";
        }
        break;
      case "role_name":
        if (!value) {
          error = "Please select a role.";
        }
        break;
      default:
        break;
    }
    setErrors((prev) => ({ ...prev, [name]: error }));
    return error;
  };

  // Improve validateAllFields to be more thorough
  const validateAllFields = () => {
    let valid = true;
    const fieldNames = [
      "first_name",
      "last_name",
      "phone",
      "email",
      "role_name",
    ];

    const newErrors: { [key: string]: string } = {};

    fieldNames.forEach((field) => {
      const value = formData[field as keyof typeof formData] as string;
      const error = validateField(field, value);
      if (error) {
        valid = false;
        newErrors[field] = error;
      }
    });

    setErrors(newErrors);
    return valid;
  };

  // When opening the modal, strip the +91 prefix for the form
  const openEditModal = () => {
    // Create a copy with phone number without +91 prefix for editing
    setFormData({
      ...userData,
    });
    setErrors({}); // Reset errors when opening modal
    openModal();
  };

  // Add function to fetch roles
  const fetchRoles = async () => {
    try {
      const response = await userApi.getRoles();
      setRoles(response.data);
    } catch (error) {
      console.error("Error fetching roles:", error);
    }
  };

  const fetchUserProfile = async () => {
    try {
      const response = await userApi.getProfile();
      setUserData(response.data.user);
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  };

  // Update handleChange to modify formData instead of userData and validate
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
    validateField(name, value);
  };

  // Add handleBlur for validation on field blur
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    validateField(name, value);
  };

  // Modify handleSave to ensure validation runs before submission
  const handleSave = async (event: React.FormEvent) => {
    event.preventDefault();

    // Force validation of all fields
    if (!validateAllFields()) {
      toast.error("Please fix all errors before saving");
      return;
    }

    try {
      // Create data to send
      const dataToSend = {
        ...formData,
      };

      const updateResponse = await userApi.updateProfile(dataToSend);

      console.log("Response:", updateResponse.data);
      toast.success("Profile updated successfully");
      closeModal();
      fetchUserProfile();
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    }
  };

  return (
    <div className="p-5 border border-gray-200 rounded-2xl dark:border-gray-800 lg:p-6">
      <div className="flex flex-col gap-6 lg:flex-row lg:items-start lg:justify-between">
        <div>
          <h4 className="text-lg font-semibold text-gray-800 dark:text-white/90 lg:mb-6">
            Personal Information
          </h4>

          <div className="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 2xl:gap-x-32">
            <div>
              <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                First Name
              </p>
              <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                {userData.first_name}
              </p>
            </div>

            <div>
              <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                Last Name
              </p>
              <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                {userData.last_name}
              </p>
            </div>

            <div>
              <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                Email address
              </p>
              <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                {userData.email}
              </p>
            </div>

            <div>
              <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                Phone
              </p>
              <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                {userData.phone}
              </p>
            </div>

            <div>
              <p className="mb-2 text-xs leading-normal text-gray-500 dark:text-gray-400">
                Role
              </p>
              <p className="text-sm font-medium text-gray-800 dark:text-white/90">
                {userData.role_name}
              </p>
            </div>
          </div>
        </div>
        <button
          onClick={openEditModal}
          className="flex w-full items-center justify-center gap-2 rounded-full border border-gray-300 bg-white px-4 py-3 text-sm font-medium text-gray-700 shadow-theme-xs hover:bg-gray-50 hover:text-gray-800 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:hover:bg-white/[0.03] dark:hover:text-gray-200 lg:inline-flex lg:w-auto"
        >
          <svg
            className="fill-current"
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M15.0911 2.78206C14.2125 1.90338 12.7878 1.90338 11.9092 2.78206L4.57524 10.116C4.26682 10.4244 4.0547 10.8158 3.96468 11.2426L3.31231 14.3352C3.25997 14.5833 3.33653 14.841 3.51583 15.0203C3.69512 15.1996 3.95286 15.2761 4.20096 15.2238L7.29355 14.5714C7.72031 14.4814 8.11172 14.2693 8.42013 13.9609L15.7541 6.62695C16.6327 5.74827 16.6327 4.32365 15.7541 3.44497L15.0911 2.78206ZM12.9698 3.84272C13.2627 3.54982 13.7376 3.54982 14.0305 3.84272L14.6934 4.50563C14.9863 4.79852 14.9863 5.2734 14.6934 5.56629L14.044 6.21573L12.3204 4.49215L12.9698 3.84272ZM11.2597 5.55281L5.6359 11.1766C5.53309 11.2794 5.46238 11.4099 5.43238 11.5522L5.01758 13.5185L6.98394 13.1037C7.1262 13.0737 7.25666 13.003 7.35947 12.9002L12.9833 7.27639L11.2597 5.55281Z"
              fill=""
            />
          </svg>
          Edit
        </button>
      </div>

      {/* Edit Profile Modal */}
      <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[700px] m-4">
        <div className="no-scrollbar relative w-full max-w-[700px] overflow-y-auto rounded-3xl bg-white p-4 dark:bg-gray-900 lg:p-11">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-semibold text-gray-800 dark:text-white/90">
              Edit Personal Information
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              Update your details to keep your profile up-to-date.
            </p>
          </div>
          <form className="flex flex-col" onSubmit={handleSave}>
            <div className="custom-scrollbar h-[450px] overflow-y-auto px-2 pb-3">
              <div className="mt-7">
                <h5 className="mb-5 text-lg font-medium text-gray-800 dark:text-white/90 lg:mb-6">
                  Personal Information
                </h5>

                <div className="grid grid-cols-1 gap-x-6 gap-y-5 lg:grid-cols-2">
                  <div className="col-span-2 lg:col-span-1">
                    <Label>First Name</Label>
                    <Input
                      type="text"
                      name="first_name"
                      value={formData.first_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.first_name}
                      hint={errors.first_name}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Last Name</Label>
                    <Input
                      type="text"
                      name="last_name"
                      value={formData.last_name}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.last_name}
                      hint={errors.last_name}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Email Address</Label>
                    <Input
                      type="text"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={!!errors.email}
                      hint={errors.email}
                    />
                  </div>

                  <div className="col-span-2 lg:col-span-1">
                    <Label>Phone</Label>
                    <Input
                      type="text"
                      name="phone"
                      value={formData.phone}
                      onChange={handleChange}
                      onBlur={handleBlur}
                      maxLength={10}
                      error={!!errors.phone}
                      hint={errors.phone}
                    />
                  </div>

                  <div className="col-span-2">
                    <Label>Role</Label>
                    <div
                      className={`relative ${errors.role_name ? "mb-6" : ""}`}
                    >
                      <select
                        name="role_name"
                        value={formData.role_name || ""}
                        onChange={(e) => {
                          setFormData({
                            ...formData,
                            role_name: e.target.value,
                          });
                          validateField("role_name", e.target.value);
                        }}
                        onBlur={(e) =>
                          validateField("role_name", e.target.value)
                        }
                        className={`w-full px-4 py-3 border rounded-lg focus:outline-none focus:ring-2 ${
                          errors.role_name
                            ? "border-red-500 focus:ring-red-200"
                            : "border-gray-300 focus:ring-blue-200"
                        }`}
                      >
                        <option value="">Select Role</option>
                        {roles.map((role) => (
                          <option key={role.id} value={role.role_name}>
                            {role.role_name}
                          </option>
                        ))}
                      </select>
                      {errors.role_name && (
                        <p className="absolute text-xs text-red-500 mt-1">
                          {errors.role_name}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button size="sm" variant="outline" onClick={closeModal}>
                Close
              </Button>
              <Button size="sm" type="submit">
                Save Changes
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </div>
  );
}
