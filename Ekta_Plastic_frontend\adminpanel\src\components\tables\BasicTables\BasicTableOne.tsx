// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHeader,
//   TableRow,
// } from "../../ui/table";
// import Badge from "../../ui/badge/Badge";
// import { Trash, ChevronUp, ChevronDown, Pencil } from "lucide-react";
// import { useState } from "react";

// // Props Interface
// interface BasicTableOneProps {
//   userList: {
//     id: number;
//     first_name: string;
//     last_name: string;
//     phone: string;
//     email: string;
//     password: string;
//     role_name: string;

//     hotel_name?: string;
//     Role?: {
//       role_name: string;
//     };
//   }[];
//   onEdit: (user: {
//     id: number;
//     first_name: string;
//     last_name: string;
//     phone: string;
//     email: string;
//     password: string;
//     role_name: string;

//     hotel_name?: string;
//     Role?: {
//       role_name: string;
//     };
//   }) => void;

//   onDelete: (id: number) => void;
//   startIndex: number;
//   onSort?: (field: string) => void;
//   sortField?: string;
//   sortDirection?: "asc" | "desc";
//   searchTerm?: string; // Add search term for highlighting
// }

// export default function BasicTableOne({
//   userList,
//   onEdit,
//   onDelete,
//   startIndex = 1,
//   onSort,
//   sortField = "",
//   sortDirection = "asc",
//   searchTerm = "",
// }: BasicTableOneProps) {
//   const [hoverDeleteId, setHoverDeleteId] = useState<number | null>(null);
//   const [selectedRows, setSelectedRows] = useState<number[]>([]);
//   const [selectAll, setSelectAll] = useState(false);
//   const handleSortClick = (field: string) => {
//     if (onSort) {
//       onSort(field);
//     }
//   };

//   // Render sort icon only for first_name column
//   const renderSortIcon = (field: string) => {
//     if (field !== "first_name") return null;

//     if (sortField === field) {
//       return sortDirection === "asc" ? (
//         <ChevronUp className="inline-block ml-1 h-4 w-4" />
//       ) : (
//         <ChevronDown className="inline-block ml-1 h-4 w-4" />
//       );
//     }
//   };
//   // const handleSortClick = (field: string) => {
//   //   if (field === "first_name" && onSort) {
//   //     onSort(field);
//   //   }
//   // };

//   // const renderSortIcon = (field: string) => {
//   //   if (field !== "first_name" || sortField !== field) return null;

//   //   return sortDirection === "asc" ? (
//   //     <ChevronUp className="inline-block ml-1 h-4 w-4" />
//   //   ) : (
//   //     <ChevronDown className="inline-block ml-1 h-4 w-4" />
//   //   );
//   // };

//   const toggleSelectAll = () => {
//     if (selectAll) {
//       setSelectedRows([]);
//     } else {
//       setSelectedRows(userList.map((user) => user.id));
//     }
//     setSelectAll(!selectAll);
//   };

//   const toggleSelectRow = (id: number) => {
//     if (selectedRows.includes(id)) {
//       setSelectedRows(selectedRows.filter((rowId) => rowId !== id));
//       setSelectAll(false);
//     } else {
//       setSelectedRows([...selectedRows, id]);
//       if (selectedRows.length + 1 === userList.length) {
//         setSelectAll(true);
//       }
//     }
//   };

//   const handleDeleteSelected = () => {
//     // Implement bulk delete functionality here
//     selectedRows.forEach((id) => onDelete(id));
//     setSelectedRows([]);
//     setSelectAll(false);
//   };

//   // Function to get user initials for avatar
//   const getUserInitials = (firstName: string, lastName: string) => {
//     return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
//   };

//   // Function to highlight search term in text
//   const highlightText = (text: string, highlight: string) => {
//     if (!highlight.trim() || !text) return <span>{text}</span>;

//     const regex = new RegExp(`(${highlight.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
//     const parts = text.split(regex);

//     return (
//       <>
//         {parts.map((part, index) => (
//           regex.test(part) ?
//             <span key={index} className="bg-yellow-200 dark:bg-yellow-700">{part}</span> :
//             <span key={index}>{part}</span>
//         ))}
//       </>
//     );
//   };

//   // Function to generate a consistent color based on user name
//   const getAvatarColor = (name: string) => {
//     const colors = [
//       "bg-blue-500",
//       "bg-green-500",
//       "bg-purple-500",
//       "bg-pink-500",
//       "bg-indigo-500",
//       "bg-yellow-500",
//     ];

//     // Simple hash function to get consistent color
//     const hash = name
//       .split("")
//       .reduce((acc, char) => acc + char.charCodeAt(0), 0);
//     return colors[hash % colors.length];
//   };

//   return (
//     <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
//       {selectedRows.length > 0 && (
//         <div className="p-4 bg-gray-50 dark:bg-gray-800 flex justify-between items-center">
//           <span className="text-sm text-gray-700 dark:text-gray-300">
//             {selectedRows.length} item(s) selected
//           </span>
//           <button
//             onClick={handleDeleteSelected}
//             className="px-3 py-1 flex items-center gap-2 text-red-600 hover:text-red-700 transition-colors"
//           >
//             <Trash size={18} />
//             {/* <span>Delete Selected</span> */}
//           </button>
//         </div>
//       )}

//       <div className="max-w-full overflow-x-auto">
//         <div className="min-w-[1102px]">
//           <Table>
//             {/* Table Header */}
//             <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
//               <TableRow>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-theme-xs dark:text-gray-400 w-12 text-center"
//                 >
//                   <input
//                     type="checkbox"
//                     checked={selectAll}
//                     onChange={toggleSelectAll}
//                     className="w-4 h-4 rounded border-gray-300 mx-auto"
//                   />
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
//                 >
//                   Sr. No
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400 cursor-pointer"
//                   onClick={() => handleSortClick("first_name")}
//                 >
//                   <div className="flex items-center pl-4">
//                     User {renderSortIcon("first_name")}
//                   </div>
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
//                 >
//                   Phone
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
//                 >
//                   Role
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
//                 >
//                   Hotel Name
//                 </TableCell>
//                 <TableCell
//                   isHeader
//                   className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
//                 >
//                   Actions
//                 </TableCell>
//               </TableRow>
//             </TableHeader>

//             {/* Table Body */}
//             <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
//               {userList.map((user, index) => (
//                 <TableRow key={user.id}>
//                   <TableCell className="px-5 py-4 sm:px-6 w-12 text-center">
//                     <input
//                       type="checkbox"
//                       checked={selectedRows.includes(user.id)}
//                       onChange={() => toggleSelectRow(user.id)}
//                       className="w-4 h-4 rounded border-gray-300 mx-auto"
//                     />
//                   </TableCell>
//                   <TableCell className="px-5 py-4 sm:px-6 text-start">
//                     <span className="block text-gray-500 text-theme-sm dark:text-gray-400">
//                       {startIndex + index}
//                     </span>
//                   </TableCell>

//                   {/* In the table body */}
//                   <TableCell className="px-5 py-4 sm:px-6 text-start">
//                     <div className="flex items-center gap-3 pl-0.5">
//                       <div
//                         className={`w-10 h-10 rounded-full flex items-center justify-center text-white ${getAvatarColor(
//                           user.first_name + user.last_name
//                         )}`}
//                       >
//                         {getUserInitials(user.first_name, user.last_name)}
//                       </div>
//                       <div className="flex flex-col">
//                         <span className="block font-medium text-gray-700 text-theme-sm dark:text-white/90">
//                           {highlightText(`${user.first_name} ${user.last_name}`, searchTerm)}
//                         </span>
//                         <span className="text-xs text-gray-500 dark:text-gray-400">
//                           {highlightText(user.email, searchTerm)}
//                         </span>
//                       </div>
//                     </div>
//                   </TableCell>
//                   <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
//                     {highlightText(user.phone, searchTerm)}
//                   </TableCell>
//                   <TableCell className="px-4 py-3 text-gray-500 text-theme-sm dark:text-gray-400">
//                     <Badge
//                       size="sm"
//                       color={
//                         (
//                           user.Role?.role_name || user.role_name
//                         ).toLowerCase() === "admin"
//                           ? "primary"
//                           : (
//                               user.Role?.role_name || user.role_name
//                             ).toLowerCase() === "customer"
//                           ? "success"
//                           : (
//                               user.Role?.role_name || user.role_name
//                             ).toLowerCase() === "hotel master"
//                           ? "warning"
//                           : (
//                               user.Role?.role_name || user.role_name
//                             ).toLowerCase() === "manager"
//                           ? "error"
//                           : "info"
//                       }
//                     >
//                       {user.Role?.role_name || user.role_name}
//                     </Badge>
//                   </TableCell>
//                   <TableCell className="px-4 py-3 text-gray-500 text-theme-sm dark:text-gray-400">
//                     {user.hotel_name ? highlightText(user.hotel_name, searchTerm) : "-"}
//                   </TableCell>

//                   <TableCell className="px-4 py-3 text-gray-500 text-theme-sm dark:text-gray-400">
//                     <div className="flex gap-1">

//                       <button
//                         onClick={() => onEdit(user)}
//                         className="p-1 text-gray-900 hover:text-blue-600 transition-colors dark:text-gray-200"
//                         title="Edit"
//                       >
//                         <Pencil size={20} />
//                       </button>
//                       <button
//                         onClick={() => onDelete(user.id)}
//                         onMouseEnter={() => setHoverDeleteId(user.id)}
//                         onMouseLeave={() => setHoverDeleteId(null)}
//                         className={`p-1 ${
//                           hoverDeleteId === user.id
//                             ? "text-red-500"
//                             : "text-gray-900 dark:text-gray-200"
//                         } hover:text-red-500 transition-colors`}
//                         title="Delete"
//                       >
//                         <Trash size={20} />
//                       </button>
//                     </div>
//                   </TableCell>
//                 </TableRow>
//               ))}

//               {userList.length === 0 && (
//                 <TableRow>
//                   <TableCell
//                     colSpan={6}
//                     className="px-4 py-8 text-center text-gray-500"
//                   >
//                     No users found
//                   </TableCell>
//                 </TableRow>
//               )}
//             </TableBody>
//           </Table>
//         </div>
//       </div>
//     </div>
//   );
// }
