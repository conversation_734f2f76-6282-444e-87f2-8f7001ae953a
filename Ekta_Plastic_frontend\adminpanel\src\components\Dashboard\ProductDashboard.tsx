import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import PageBreadcrumb from "../common/PageBreadCrumb";
import ComponentCard from "../common/ComponentCard";
import PageMeta from "../common/PageMeta";
import { userApi } from "../../services/api";
import { Package, Image, Grid3X3, TrendingUp } from "lucide-react";

interface DashboardStats {
  totalProducts: number;
  totalCategories: number;
  totalImages: number;
  bestsellerProducts: number;
}

const ProductDashboard = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalProducts: 0,
    totalCategories: 0,
    totalImages: 0,
    bestsellerProducts: 0,
  });
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setIsLoading(true);
      
      // Fetch all data in parallel
      const [productsResponse, categoriesResponse, imagesResponse] = await Promise.all([
        userApi.getAllProducts(),
        userApi.getAllProductCategories(),
        userApi.getAllProductImages(),
      ]);

      const products = productsResponse.data || [];
      const categories = categoriesResponse.data || [];
      const images = imagesResponse.data || [];

      // Calculate bestseller products
      const bestsellerCount = products.filter((product: any) => product.isbestsellerproduct).length;

      setStats({
        totalProducts: products.length,
        totalCategories: categories.length,
        totalImages: images.length,
        bestsellerProducts: bestsellerCount,
      });
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const StatCard = ({ 
    title, 
    value, 
    icon, 
    color, 
    link 
  }: { 
    title: string; 
    value: number; 
    icon: React.ReactNode; 
    color: string; 
    link: string; 
  }) => (
    <Link to={link} className="block">
      <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 hover:shadow-md transition-shadow duration-200`}>
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
            <p className="text-3xl font-bold text-gray-900 dark:text-white mt-2">
              {isLoading ? "..." : value.toLocaleString()}
            </p>
          </div>
          <div className={`p-3 rounded-full ${color}`}>
            {icon}
          </div>
        </div>
      </div>
    </Link>
  );

  return (
    <>
      <PageMeta
        title="Dashboard - Ekta Plastic"
        description="Product management dashboard for Ekta Plastic"
      />
      <PageBreadcrumb pageTitle="Dashboard" />

      <div className="space-y-6">
        {/* Welcome Section */}
        <ComponentCard title="Welcome to Ekta Plastic Product Management">
          <div className="text-center py-8">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Product Management Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Manage your product catalog, categories, and images efficiently. 
              Get insights into your product inventory and track bestsellers.
            </p>
          </div>
        </ComponentCard>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <StatCard
            title="Total Products"
            value={stats.totalProducts}
            icon={<Package size={24} className="text-white" />}
            color="bg-blue-500"
            link="/products"
          />
          
          <StatCard
            title="Product Categories"
            value={stats.totalCategories}
            icon={<Grid3X3 size={24} className="text-white" />}
            color="bg-green-500"
            link="/product-categories"
          />
          
          <StatCard
            title="Product Images"
            value={stats.totalImages}
            icon={<Image size={24} className="text-white" />}
            color="bg-purple-500"
            link="/product-images"
          />
          
          <StatCard
            title="Bestseller Products"
            value={stats.bestsellerProducts}
            icon={<TrendingUp size={24} className="text-white" />}
            color="bg-orange-500"
            link="/products"
          />
        </div>

        {/* Quick Actions */}
        <ComponentCard title="Quick Actions">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link
              to="/product-categories"
              className="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200"
            >
              <Grid3X3 size={20} className="text-blue-600 dark:text-blue-400 mr-3" />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Manage Categories</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Add, edit, or delete product categories</p>
              </div>
            </Link>

            <Link
              to="/products"
              className="flex items-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200"
            >
              <Package size={20} className="text-green-600 dark:text-green-400 mr-3" />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Manage Products</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Add, edit, or delete products</p>
              </div>
            </Link>

            <Link
              to="/product-images"
              className="flex items-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-200"
            >
              <Image size={20} className="text-purple-600 dark:text-purple-400 mr-3" />
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white">Manage Images</h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">Upload and manage product images</p>
              </div>
            </Link>
          </div>
        </ComponentCard>

        {/* Recent Activity */}
        <ComponentCard title="Getting Started">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                <span className="text-blue-600 dark:text-blue-400 font-semibold text-sm">1</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Create Product Categories</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Start by creating categories to organize your products effectively.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <span className="text-green-600 dark:text-green-400 font-semibold text-sm">2</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Add Products</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Add your products with detailed information including prices, descriptions, and specifications.
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0 w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                <span className="text-purple-600 dark:text-purple-400 font-semibold text-sm">3</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white">Upload Product Images</h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Enhance your products with high-quality images to attract customers.
                </p>
              </div>
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
};

export default ProductDashboard;
